import {
  Controller,
  Get,
  UseGuards,
  Param,
  Body,
  Put,
  Patch,
} from '@nestjs/common';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { Roles } from './auth/roles.decorator';
import { RolesGuard } from './auth/roles.guard';
import { PrismaService } from './prisma.service';

@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('ADMIN')
export class AdminController {
  constructor(private prisma: PrismaService) {}

  @Get('dashboard')
  async getDashboard() {
    const users = await this.prisma.user.findMany({
      select: { id: true, name: true, email: true, role: true },
      orderBy: { createdAt: 'desc' },
    });
    const products = await this.prisma.product.findMany({
      select: {
        id: true,
        name: true,
        image: true,
        category: true,
        slug: true,
        price: true,
      },
      orderBy: { id: 'desc' },
    });
    const orders = await this.prisma.order.findMany({
      include: {
        user: { select: { id: true, name: true, email: true } },
        product: { select: { id: true, name: true, price: true, image: true } },
      },
      orderBy: { createdAt: 'desc' },
    });
    const stats = {
      users: users.length,
      orders: orders.length,
      products: products.length,
      collectedOrders: orders.filter((o) => o.status === 'COLLECTED').length,
    };
    return { users, products, orders, stats };
  }

  @Get('users')
  async getUsers() {
    return await this.prisma.user.findMany({
      select: { id: true, name: true, email: true, role: true },
      orderBy: { createdAt: 'desc' },
    });
  }

  @Get('products/:id')
  async getProductById(@Param('id') id: string) {
    // Find product by numeric id or slug
    const product = await this.prisma.product.findUnique({
      where: { id: Number(id) },
    });
    if (!product) {
      throw new Error('Product not found');
    }
    return product;
  }

  @Put('products/:id')
  async updateProductById(@Param('id') id: string, @Body() data: any) {
    // Update product by numeric id
    const product = await this.prisma.product.update({
      where: { id: Number(id) },
      data,
    });
    return product;
  }

  @Get('orders')
  async getOrders() {
    return await this.prisma.order.findMany({
      include: {
        user: { select: { id: true, name: true, email: true } },
        product: { select: { id: true, name: true, price: true, image: true } },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  @Put('orders/:id/collected')
  async markOrderCollected(@Param('id') id: string) {
    return await this.prisma.order.update({
      where: { id: Number(id) },
      data: { status: 'COLLECTED' },
    });
  }

  @Patch('orders/:id/status')
  async updateOrderStatus(
    @Param('id') id: string,
    @Body() body: { status: string },
  ) {
    return await this.prisma.order.update({
      where: { id: Number(id) },
      data: { status: body.status as any },
    });
  }

  @Put('users/:id/role')
  async updateUserRole(
    @Param('id') id: string,
    @Body() body: { role: string },
  ) {
    // Validate role
    if (!['USER', 'ADMIN'].includes(body.role)) {
      throw new Error('Invalid role. Must be USER or ADMIN');
    }

    return await this.prisma.user.update({
      where: { id: Number(id) },
      data: { role: body.role as any },
      select: { id: true, name: true, email: true, role: true },
    });
  }
}
