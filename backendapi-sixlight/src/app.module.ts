import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ProductModule } from './product/product.module';
import { AdminController } from './admin.controller';
import { UserController } from './user.controller';
import { AuthModule } from './auth/auth.module';
import { PrismaService } from './prisma.service';
import { CategoryController } from './category/category.controller';
import { CategoryService } from './category/category.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    ProductModule,
    AuthModule,
  ],
  controllers: [
    App<PERSON><PERSON>roller,
    AdminController,
    UserController,
    CategoryController,
  ],
  providers: [AppService, PrismaService, CategoryService],
})
export class AppModule {}
