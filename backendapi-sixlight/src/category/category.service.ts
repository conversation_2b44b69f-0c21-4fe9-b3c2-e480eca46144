import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class CategoryService {
  private prisma = new PrismaClient();

  async findAll() {
    return await this.prisma.category.findMany();
  }

  async create(name: string) {
    return await this.prisma.category.create({ data: { name } });
  }

  async delete(name: string) {
    return await this.prisma.category.delete({ where: { name } });
  }
}
