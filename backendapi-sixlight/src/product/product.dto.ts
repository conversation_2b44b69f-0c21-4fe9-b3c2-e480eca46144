import { IsString, IsN<PERSON>ber, IsBoolean, IsOptional } from 'class-validator';

export class CreateProductDto {
  @IsString()
  name: string;

  @IsString()
  image: string;

  @IsString()
  description: string;

  @IsBoolean()
  customizable: boolean;

  @IsString()
  slug: string;

  @IsNumber()
  categoryId: number;

  @IsOptional()
  @IsString()
  modelUrl?: string;

  @IsNumber()
  price: number;
}

export class UpdateProductDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  image?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  customizable?: boolean;

  @IsOptional()
  @IsString()
  slug?: string;

  @IsOptional()
  @IsNumber()
  categoryId?: number;

  @IsOptional()
  @IsString()
  modelUrl?: string;

  @IsOptional()
  @IsNumber()
  price?: number;
}
