import { Injectable } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { CreateProductDto, UpdateProductDto } from './product.dto';

@Injectable()
export class ProductService {
  private prisma = new PrismaClient();

  async findAll() {
    return this.prisma.product.findMany();
  }

  async findOne(slug: string) {
    return this.prisma.product.findUnique({
      where: { slug },
    });
  }

  async create(data: CreateProductDto) {
    // Convert categoryId to category string if needed
    const { categoryId, ...rest } = data as any;

    // Get the category name from the categoryId
    let categoryName = 'Uncategorized';
    if (categoryId) {
      try {
        const categoryRecord = await this.prisma.category.findUnique({
          where: { id: categoryId },
        });
        if (categoryRecord) {
          categoryName = categoryRecord.name;
        }
      } catch (error) {
        console.error('Error fetching category:', error);
      }
    }

    return this.prisma.product.create({
      data: {
        ...rest,
        category: categoryName,
      },
    });
  }

  async update(slug: string, data: UpdateProductDto) {
    const { categoryId, ...rest } = data as any;

    // If categoryId is provided, get the category name
    let updateData = { ...rest };

    if (categoryId !== undefined) {
      try {
        const categoryRecord = await this.prisma.category.findUnique({
          where: { id: categoryId },
        });
        if (categoryRecord) {
          updateData.category = categoryRecord.name;
        }
      } catch (error) {
        console.error('Error fetching category:', error);
      }
    }

    return this.prisma.product.update({
      where: { slug },
      data: updateData,
    });
  }

  async delete(slug: string) {
    return this.prisma.product.delete({ where: { slug } });
  }
}
