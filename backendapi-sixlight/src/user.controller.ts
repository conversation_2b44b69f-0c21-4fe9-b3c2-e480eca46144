import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  UseGuards,
  Request,
  Body,
} from '@nestjs/common';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { PrismaService } from './prisma.service';
import * as bcrypt from 'bcryptjs';

@Controller('user')
@UseGuards(JwtAuthGuard)
export class UserController {
  constructor(private prisma: PrismaService) {}

  @Get('dashboard')
  async getDashboard(@Request() req) {
    // Use correct user identifier from JWT payload
    const userId = req.user?.id || req.user?.userId;
    if (!userId) {
      throw new Error('User ID not found in request');
    }
    const user = await this.prisma.user.findUnique({
      where: { id: Number(userId) },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        profileImage: true,
      },
    });
    const orders = await this.prisma.order.findMany({
      where: { userId: Number(userId) },
      include: {
        product: { select: { id: true, name: true, price: true, image: true } },
      },
      orderBy: { createdAt: 'desc' },
    });
    return {
      user,
      orders,
    };
  }

  // PATCH /user/change-password
  @Patch('change-password')
  async changePassword(
    @Request() req,
    @Body() body: { oldPassword: string; newPassword: string },
  ) {
    const userId = req.user?.id || req.user?.userId;
    if (!userId) throw new Error('User ID not found in request');
    if (!body.oldPassword || !body.newPassword) {
      return { success: false, message: 'Old and new password required.' };
    }
    const user = await this.prisma.user.findUnique({
      where: { id: Number(userId) },
      select: { password: true },
    });
    if (!user) return { success: false, message: 'User not found.' };
    const isMatch = await bcrypt.compare(body.oldPassword, user.password);
    if (!isMatch) {
      return { success: false, message: 'Old password is incorrect.' };
    }
    const hashed = await bcrypt.hash(body.newPassword, 10);
    await this.prisma.user.update({
      where: { id: Number(userId) },
      data: { password: hashed },
    });
    return { success: true };
  }

  // PATCH /user/profile - update name and profile image
  @Patch('profile')
  async updateProfile(
    @Request() req,
    @Body() body: { name?: string; profileImage?: string },
  ) {
    const userId = req.user?.id || req.user?.userId;
    if (!userId) throw new Error('User ID not found in request');
    const data: { name?: string; profileImage?: string } = {};
    if (body.name !== undefined) data.name = body.name;
    if (body.profileImage !== undefined) data.profileImage = body.profileImage;
    return this.prisma.user.update({
      where: { id: Number(userId) },
      data,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        profileImage: true,
      },
    });
  }

  // DELETE /user/delete
  @Delete('delete')
  async deleteAccount(@Request() req) {
    const userId = req.user?.id || req.user?.userId;
    if (!userId) throw new Error('User ID not found in request');
    await this.prisma.user.delete({ where: { id: Number(userId) } });
    return { success: true };
  }

  // POST /user/orders - create a new order
  @Post('orders')
  async createOrder(@Request() req, @Body() body: any) {
    const userId = req.user?.id || req.user?.userId;
    if (!userId) {
      throw new Error('User ID not found in request');
    }

    const {
      productId,
      customerName,
      customerPhone,
      customerAddress,
      customColor,
      customText,
      isCustomized,
      customizationData,
      customizationPreview,
      quantity = 1,
    } = body;

    // Get product details for pricing
    const product = await this.prisma.product.findUnique({
      where: { id: Number(productId) },
    });

    if (!product) {
      throw new Error('Product not found');
    }

    // Get user email
    const user = await this.prisma.user.findUnique({
      where: { id: Number(userId) },
      select: { email: true },
    });

    const unitPrice = product.price;
    const totalPrice = unitPrice * quantity;

    const order = await this.prisma.order.create({
      data: {
        userId: Number(userId),
        productId: Number(productId),
        customerName,
        customerPhone,
        customerEmail: user?.email || '',
        customerAddress,
        customColor: isCustomized ? customColor : null,
        customText: isCustomized ? customText : null,
        isCustomized: Boolean(isCustomized),
        customizationData: isCustomized ? customizationData : null,
        customizationPreview: isCustomized ? customizationPreview : null,
        quantity,
        unitPrice,
        totalPrice,
      },
      include: {
        product: { select: { id: true, name: true, price: true, image: true } },
        user: { select: { id: true, name: true, email: true } },
      },
    });

    return order;
  }
}
