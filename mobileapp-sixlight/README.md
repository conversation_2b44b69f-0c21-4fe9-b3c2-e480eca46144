# Welcome to your Expo app 👋

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Troubleshooting

### API Connection Issues

If you see "Network request failed" errors or the app shows demo content:

1. **Check if the backend server is running**

   - The app expects a backend server at `http://localhost:3001` in development
   - Make sure your backend server is started and accessible

2. **Verify API configuration**

   - Open the debug screen: Navigate to `/debug/config` in the app
   - Check the API status and connection details
   - Use the "Test Connection" button to verify connectivity

3. **Environment Variables**

   - Set `EXPO_PUBLIC_API_URL` to your backend URL if different from default
   - For production: `EXPO_PUBLIC_API_URL=https://backendapi-sixlight.onrender.com`
   - For local development: `EXPO_PUBLIC_API_URL=http://localhost:3001`

4. **Common Solutions**
   - Restart the Expo development server: `npx expo start --clear`
   - Check your network connection
   - Verify the backend server is accessible from your device/emulator
   - For physical devices, use your computer's IP address instead of localhost

### Route Warnings

If you see warnings about missing routes like "auth", "product", or "cart":

- These are expected during development as some routes are conditionally loaded
- The app will work normally with fallback demo content

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
