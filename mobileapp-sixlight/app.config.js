/**
 * Six Light Media Store - Expo App Configuration
 * Environment-based configuration for development and production
 */

import "dotenv/config";

export default {
  expo: {
    name: "Six Light Media Store",
    slug: "sixlight-media-store",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "sixlight-store",
    userInterfaceStyle: "automatic",
    splash: {
      image: "./assets/images/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff",
    },
    assetBundlePatterns: ["**/*"],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.sixlightmedia.store",
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#ffffff",
      },
      package: "com.sixlightmedia.store",
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png",
    },
    plugins: [
      "expo-router",
      [
        "expo-image-picker",
        {
          photosPermission:
            "The app accesses your photos to let you upload product images and customize products.",
        },
      ],
      [
        "expo-media-library",
        {
          photosPermission:
            "Allow Six Light Media Store to access your photo library to upload images.",
          savePhotosPermission:
            "Allow Six Light Media Store to save photos to your photo library.",
          isAccessMediaLocationEnabled: true,
        },
      ],
    ],
    experiments: {
      typedRoutes: true,
    },
    extra: {
      // Environment Detection
      isDevelopment: process.env.NODE_ENV === "development",

      // API Configuration
      apiUrl:
        process.env.EXPO_PUBLIC_API_URL ||
        (process.env.NODE_ENV === "development"
          ? "http://localhost:3001"
          : "https://backendapi-sixlight.onrender.com"),
      apiUrlDev: process.env.EXPO_PUBLIC_API_URL_DEV || "http://localhost:3001",
      apiUrlProd:
        process.env.EXPO_PUBLIC_API_URL_PROD ||
        "https://backendapi-sixlight.onrender.com",

      // ImageKit Configuration
      imagekitUrlEndpoint:
        process.env.EXPO_PUBLIC_IMAGEKIT_URL_ENDPOINT ||
        "https://ik.imagekit.io/fwbvmq9re",
      imagekitPublicKey:
        process.env.EXPO_PUBLIC_IMAGEKIT_PUBLIC_KEY ||
        "public_kFR0vTVL7DIDI8YW9eF6/luGjB4=",
      imagekitPrivateKey:
        process.env.EXPO_PUBLIC_IMAGEKIT_PRIVATE_KEY ||
        "private_jtsBIKXyuTkkHzHJOBSHLqbyK74=",

      // Site Configuration
      siteUrl:
        process.env.EXPO_PUBLIC_SITE_URL ||
        (process.env.NODE_ENV === "development"
          ? "http://localhost:3000"
          : "https://sixlightmediastorebeta.netlify.app"),
      siteUrlDev:
        process.env.EXPO_PUBLIC_SITE_URL_DEV || "http://localhost:3000",
      siteUrlProd:
        process.env.EXPO_PUBLIC_SITE_URL_PROD ||
        "https://sixlightmediastorebeta.netlify.app",

      // Environment
      environment: process.env.NODE_ENV || "production",

      // App Information
      appName: "Six Light Media Store",
      appVersion: "1.0.0",
      appDescription:
        "Mobile e-commerce app for Six Light Media with product customization",

      // Feature Flags
      enableImageUpload: true,
      enableProductCustomization: true,
      enableAdminPanel: true,
      enablePushNotifications: false, // Can be enabled later

      // Analytics (for future use)
      enableAnalytics: false,

      // Debug Settings
      enableDebugMode: process.env.NODE_ENV === "development",
      enableApiLogging: process.env.NODE_ENV === "development",
    },
  },
};
