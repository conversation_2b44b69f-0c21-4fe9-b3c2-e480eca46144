{"expo": {"name": "Six Light Media Store", "slug": "sixlight-media-store", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "sixlightstore", "userInterfaceStyle": "automatic", "newArchEnabled": true, "primaryColor": "#3B82F6", "backgroundColor": "#ffffff", "ios": {"supportsTablet": true, "bundleIdentifier": "com.sixlightmedia.store", "buildNumber": "1.0.0", "infoPlist": {"UIBackgroundModes": ["background-fetch"], "NSCameraUsageDescription": "This app uses camera to scan products and take photos for customization.", "NSPhotoLibraryUsageDescription": "This app accesses photo library to select images for product customization."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#3B82F6"}, "edgeToEdgeEnabled": true, "package": "com.sixlightmedia.store", "versionCode": 1, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "INTERNET", "ACCESS_NETWORK_STATE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/6 Light Logo.png", "imageWidth": 300, "resizeMode": "contain", "backgroundColor": "#3B82F6"}], "expo-font"], "experiments": {"typedRoutes": true}, "extra": {"apiUrl": "http://localhost:3000", "eas": {"projectId": "sixlight-media-store"}}}}