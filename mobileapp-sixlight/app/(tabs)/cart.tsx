/**
 * Six Light Media Store - Cart Screen
 * Shopping cart with item management and checkout
 */

import React from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { formatPrice } from "@/services/api";

export default function CartScreen() {
  const { colors } = useTheme();
  const { state, removeFromCart, updateCartItem } = useApp();
  const router = useRouter();

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      Alert.alert(
        "Remove Item",
        "Are you sure you want to remove this item from your cart?",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Remove",
            style: "destructive",
            onPress: () => removeFromCart(itemId),
          },
        ]
      );
    } else {
      updateCartItem(itemId, newQuantity);
    }
  };

  const handleRemoveItem = (itemId: string, productName: string) => {
    Alert.alert("Remove Item", `Remove "${productName}" from your cart?`, [
      { text: "Cancel", style: "cancel" },
      {
        text: "Remove",
        style: "destructive",
        onPress: () => removeFromCart(itemId),
      },
    ]);
  };

  const handleCheckout = () => {
    router.push("/checkout");
  };

  const renderCartItem = ({ item }: { item: any }) => (
    <Card variant="default" padding="md" style={styles.cartItem}>
      <View style={styles.itemContent}>
        {/* Product Image */}
        <View
          style={[
            styles.itemImage,
            { backgroundColor: colors.background.secondary },
          ]}
        >
          {item.product?.image ? (
            <Image
              source={{ uri: item.product.image }}
              style={styles.productImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.imagePlaceholder}>
              <Text
                style={[
                  styles.placeholderIcon,
                  { color: colors.secondary[500] },
                ]}
              >
                📦
              </Text>
            </View>
          )}
        </View>

        {/* Product Details */}
        <View style={styles.itemDetails}>
          <Text style={[styles.itemName, { color: colors.text.primary }]}>
            {item.product?.name || item.name}
          </Text>
          <Text style={[styles.itemPrice, { color: colors.primary[500] }]}>
            {formatPrice(item.price)} each
          </Text>
          <Text style={[styles.itemTotal, { color: colors.text.primary }]}>
            Total: {formatPrice(item.price * item.quantity)}
          </Text>

          {/* Customization Info */}
          {item.customization && (
            <View
              style={[
                styles.customBadge,
                { backgroundColor: colors.primary[100] },
              ]}
            >
              <Text
                style={[styles.customBadgeText, { color: colors.primary[700] }]}
              >
                ✨ Customized
              </Text>
            </View>
          )}
        </View>

        {/* Quantity Controls */}
        <View style={styles.quantityControls}>
          <TouchableOpacity
            style={[
              styles.quantityButton,
              { backgroundColor: colors.background.secondary },
            ]}
            onPress={() => handleQuantityChange(item.id, item.quantity - 1)}
          >
            <Text
              style={[
                styles.quantityButtonText,
                { color: colors.text.primary },
              ]}
            >
              ➖
            </Text>
          </TouchableOpacity>

          <Text style={[styles.quantity, { color: colors.text.primary }]}>
            {item.quantity}
          </Text>

          <TouchableOpacity
            style={[
              styles.quantityButton,
              { backgroundColor: colors.primary[500] },
            ]}
            onPress={() => handleQuantityChange(item.id, item.quantity + 1)}
          >
            <Text style={[styles.quantityButtonText, { color: colors.white }]}>
              ➕
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Remove Button */}
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() =>
          handleRemoveItem(item.id, item.product?.name || item.name)
        }
      >
        <Text style={[styles.removeButtonText, { color: colors.error }]}>
          🗑️ Remove
        </Text>
      </TouchableOpacity>
    </Card>
  );

  if (state.cart.length === 0) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
            Shopping Cart
          </Text>
        </View>

        <View style={styles.emptyContainer}>
          <View
            style={[
              styles.emptyIcon,
              { backgroundColor: colors.secondary[200] },
            ]}
          >
            <Text style={styles.emptyIconText}>🛒</Text>
          </View>
          <Text style={[styles.emptyTitle, { color: colors.text.primary }]}>
            Your cart is empty
          </Text>
          <Text
            style={[styles.emptySubtitle, { color: colors.text.secondary }]}
          >
            Add some amazing products to get started
          </Text>
          <Button
            title="Start Shopping"
            onPress={() => router.push("/explore")}
            variant="primary"
            style={styles.startShoppingButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          Shopping Cart
        </Text>
        <Text style={[styles.itemCount, { color: colors.text.secondary }]}>
          {state.cartCount} item{state.cartCount !== 1 ? "s" : ""}
        </Text>
      </View>

      {/* Cart Items */}
      <FlatList
        data={state.cart}
        renderItem={renderCartItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.cartList}
        showsVerticalScrollIndicator={false}
      />

      {/* Cart Summary */}
      <View
        style={[
          styles.summaryContainer,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <Card variant="elevated" padding="lg" style={styles.summaryCard}>
          <View style={styles.summaryRow}>
            <Text
              style={[styles.summaryLabel, { color: colors.text.secondary }]}
            >
              Subtotal
            </Text>
            <Text style={[styles.summaryValue, { color: colors.text.primary }]}>
              {formatPrice(state.cartTotal)}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text
              style={[styles.summaryLabel, { color: colors.text.secondary }]}
            >
              Shipping
            </Text>
            <Text style={[styles.summaryValue, { color: colors.text.primary }]}>
              Free
            </Text>
          </View>

          <View
            style={[
              styles.summaryDivider,
              { backgroundColor: colors.border.light },
            ]}
          />

          <View style={styles.summaryRow}>
            <Text style={[styles.totalLabel, { color: colors.text.primary }]}>
              Total
            </Text>
            <Text style={[styles.totalValue, { color: colors.primary[500] }]}>
              {formatPrice(state.cartTotal)}
            </Text>
          </View>

          <Button
            title="Proceed to Checkout"
            onPress={handleCheckout}
            variant="primary"
            size="lg"
            fullWidth
            style={styles.checkoutButton}
          />
        </Card>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
  },
  itemCount: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  cartList: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.lg,
  },
  cartItem: {
    marginBottom: Spacing.md,
  },
  itemContent: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Spacing.sm,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.md,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.md,
  },
  imagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.md,
    justifyContent: "center",
    alignItems: "center",
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.md,
  },
  placeholderIcon: {
    fontSize: 32,
  },
  itemDetails: {
    flex: 1,
    marginRight: Spacing.md,
  },
  itemName: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  itemPrice: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  itemTotal: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  customBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
    alignSelf: "flex-start",
    marginTop: Spacing.xs,
  },
  customBadgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: "600",
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center",
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  quantityButtonText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
  },
  quantity: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginHorizontal: Spacing.md,
    minWidth: 30,
    textAlign: "center",
  },
  removeButton: {
    alignSelf: "flex-end",
  },
  removeButtonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  summaryContainer: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.lg,
  },
  summaryCard: {
    borderTopLeftRadius: BorderRadius.xl,
    borderTopRightRadius: BorderRadius.xl,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Spacing.sm,
  },
  summaryLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  summaryValue: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  summaryDivider: {
    height: 1,
    marginVertical: Spacing.md,
  },
  totalLabel: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
  },
  totalValue: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  checkoutButton: {
    marginTop: Spacing.lg,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
  },
  emptyIcon: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: Spacing.xl,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyIconText: {
    fontSize: 48,
  },
  emptyTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    marginBottom: Spacing.sm,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.xl,
    lineHeight: 24,
  },
  startShoppingButton: {
    paddingHorizontal: Spacing.xl,
  },
});
