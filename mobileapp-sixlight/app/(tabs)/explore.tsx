/**
 * Six Light Media Store - Explore Screen
 * Product browsing with search, filters, and categories
 */

import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card, { ProductCard } from "@/components/ui/Card";
import { apiClient, Product, Category } from "@/services/api";

const { width } = Dimensions.get("window");
const PRODUCT_CARD_WIDTH = (width - Spacing.lg * 3) / 2;

export default function ExploreScreen() {
  const { colors } = useTheme();
  const {
    state,
    dispatch,
    addToCart,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
  } = useApp();
  const router = useRouter();

  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    filterProducts();
  }, [searchQuery, selectedCategory, state.products]);

  const filterProducts = () => {
    let filtered = [...state.products];

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(
        (product) =>
          product.category.toLowerCase() === selectedCategory.toLowerCase()
      );
    }

    setFilteredProducts(filtered);
  };

  const handleProductPress = (product: Product) => {
    router.push(`/product/${product.slug}`);
  };

  const handleAddToCart = (product: Product) => {
    addToCart(product);
    console.log("Added to cart:", product.name);
  };

  const handleCategoryPress = (category: Category | null) => {
    setSelectedCategory(category?.slug || null);
  };

  const handleToggleWishlist = (product: Product) => {
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  const renderProductItem = ({ item }: { item: Product }) => (
    <ProductCard
      product={item}
      onPress={() => handleProductPress(item)}
      onAddToCart={() => handleAddToCart(item)}
      onToggleWishlist={() => handleToggleWishlist(item)}
      isInWishlist={isInWishlist(item.id)}
      style={[styles.productCard, { width: PRODUCT_CARD_WIDTH }]}
    />
  );

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          Explore Products
        </Text>
      </View>

      {/* Search Bar */}
      <View style={styles.searchSection}>
        <View
          style={[
            styles.searchContainer,
            { backgroundColor: colors.background.secondary },
          ]}
        >
          <View style={styles.searchIcon}>
            <Text
              style={[styles.searchIconText, { color: colors.text.secondary }]}
            >
              🔍
            </Text>
          </View>
          <TextInput
            style={[styles.searchInput, { color: colors.text.primary }]}
            placeholder="Search products..."
            placeholderTextColor={colors.text.tertiary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setSearchQuery("")}
            >
              <Text
                style={[styles.clearIcon, { color: colors.text.secondary }]}
              >
                ❌
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Categories Filter */}
      <View style={styles.categoriesSection}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesContainer}
        >
          {/* All Categories Button */}
          <TouchableOpacity
            style={[
              styles.categoryChip,
              {
                backgroundColor:
                  selectedCategory === null
                    ? colors.primary[500]
                    : colors.background.secondary,
              },
            ]}
            onPress={() => handleCategoryPress(null)}
          >
            <Text
              style={[
                styles.categoryChipText,
                {
                  color:
                    selectedCategory === null
                      ? colors.white
                      : colors.text.secondary,
                },
              ]}
            >
              All
            </Text>
          </TouchableOpacity>

          {/* Category Chips */}
          {state.categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryChip,
                {
                  backgroundColor:
                    selectedCategory === category.slug
                      ? colors.primary[500]
                      : colors.background.secondary,
                },
              ]}
              onPress={() => handleCategoryPress(category)}
            >
              <Text
                style={[
                  styles.categoryChipText,
                  {
                    color:
                      selectedCategory === category.slug
                        ? colors.white
                        : colors.text.secondary,
                  },
                ]}
              >
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Results Header */}
      <View style={styles.resultsHeader}>
        <Text style={[styles.resultsText, { color: colors.text.secondary }]}>
          {filteredProducts.length} product
          {filteredProducts.length !== 1 ? "s" : ""} found
        </Text>

        {/* Sort/Filter Buttons */}
        <View style={styles.filterButtons}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              { backgroundColor: colors.background.secondary },
            ]}
          >
            <Text style={[styles.filterIcon, { color: colors.text.secondary }]}>
              🔄
            </Text>
            <Text
              style={[
                styles.filterButtonText,
                { color: colors.text.secondary },
              ]}
            >
              Sort
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              { backgroundColor: colors.background.secondary },
            ]}
          >
            <Text style={[styles.filterIcon, { color: colors.text.secondary }]}>
              🔧
            </Text>
            <Text
              style={[
                styles.filterButtonText,
                { color: colors.text.secondary },
              ]}
            >
              Filter
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Products Grid */}
      <FlatList
        data={filteredProducts}
        renderItem={renderProductItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        contentContainerStyle={styles.productsGrid}
        columnWrapperStyle={styles.productRow}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <View
              style={[
                styles.emptyIcon,
                { backgroundColor: colors.secondary[200] },
              ]}
            >
              <Text style={styles.emptyIconText}>🔍</Text>
            </View>
            <Text style={[styles.emptyTitle, { color: colors.text.primary }]}>
              No products found
            </Text>
            <Text
              style={[styles.emptySubtitle, { color: colors.text.secondary }]}
            >
              Try adjusting your search or filters
            </Text>
            <Button
              title="🔄 Clear Filters"
              onPress={() => {
                setSearchQuery("");
                setSelectedCategory(null);
              }}
              variant="outline"
              style={styles.clearFiltersButton}
            />
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  headerTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
  },
  searchSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.lg,
  },
  searchIcon: {
    width: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.sm,
  },
  searchIconText: {
    fontSize: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: Typography.fontSize.base,
    paddingVertical: Spacing.sm,
  },
  clearButton: {
    padding: Spacing.sm,
  },
  clearIcon: {
    fontSize: 14,
  },
  categoriesSection: {
    marginBottom: Spacing.lg,
  },
  categoriesContainer: {
    paddingHorizontal: Spacing.lg,
  },
  categoryChip: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    marginRight: Spacing.sm,
  },
  categoryChipText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  resultsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },
  resultsText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "500",
  },
  filterButtons: {
    flexDirection: "row",
    gap: Spacing.sm,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  filterIcon: {
    fontSize: 14,
    marginRight: Spacing.xs,
  },
  filterButtonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "500",
  },
  productsGrid: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing["3xl"],
  },
  productRow: {
    justifyContent: "space-between",
  },
  productCard: {
    marginBottom: Spacing.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: Spacing["3xl"],
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: Spacing.lg,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyIconText: {
    fontSize: 32,
  },
  emptyTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    marginBottom: Spacing.sm,
  },
  emptySubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.lg,
  },
  clearFiltersButton: {
    marginTop: Spacing.md,
  },
});
