/**
 * Six Light Media Store - Home Screen
 * Extraordinary home screen with modern e-commerce design
 */

import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  Dimensions,
  Image,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import Button from "@/components/ui/Button";
import Card, { ProductCard } from "@/components/ui/Card";
import { BorderRadius, Colors, Spacing, Typography } from "@/constants/Theme";
import { useApp } from "@/contexts/AppContext";
import { useTheme } from "@/contexts/ThemeContext";
import { apiClient, Category, Product } from "@/services/api";

const { width } = Dimensions.get("window");

export default function HomeScreen() {
  const { colors, isDark } = useTheme();
  const {
    state,
    dispatch,
    addToCart,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
  } = useApp();
  const router = useRouter();

  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError("");

      // Load products and categories in parallel
      const [products, categories] = await Promise.all([
        apiClient.getProducts({ limit: 20 }),
        apiClient.getCategories(),
      ]);

      dispatch({ type: "SET_PRODUCTS", payload: products });
      dispatch({ type: "SET_CATEGORIES", payload: categories });

      // Set featured products (first 6 products for now)
      dispatch({
        type: "SET_FEATURED_PRODUCTS",
        payload: products.slice(0, 6),
      });
    } catch (error) {
      console.error("Failed to load initial data:", error);

      // Set user-friendly error message
      const errorMessage =
        error instanceof Error ? error.message : "Unable to connect to server";
      setError(errorMessage);

      // Load fallback/demo data when API is unavailable
      loadFallbackData();
    } finally {
      setLoading(false);
    }
  };

  const loadFallbackData = () => {
    // Demo data to show when API is unavailable
    const demoProducts: Product[] = [
      {
        id: 1,
        name: "Custom T-Shirt",
        description: "High-quality custom printed t-shirt",
        price: 25.99,
        image: "https://via.placeholder.com/300x300?text=Custom+T-Shirt",
        category: "Apparel",
        customizable: true,
        slug: "custom-t-shirt",
      },
      {
        id: 2,
        name: "Business Cards",
        description: "Professional business cards",
        price: 15.99,
        image: "https://via.placeholder.com/300x300?text=Business+Cards",
        category: "Print",
        customizable: true,
        slug: "business-cards",
      },
      {
        id: 3,
        name: "Photo Print",
        description: "High-quality photo printing",
        price: 12.99,
        image: "https://via.placeholder.com/300x300?text=Photo+Print",
        category: "Print",
        customizable: false,
        slug: "photo-print",
      },
    ];

    const demoCategories: Category[] = [
      { id: 1, name: "Apparel" },
      { id: 2, name: "Print" },
      { id: 3, name: "Digital" },
    ];

    dispatch({ type: "SET_PRODUCTS", payload: demoProducts });
    dispatch({ type: "SET_CATEGORIES", payload: demoCategories });
    dispatch({ type: "SET_FEATURED_PRODUCTS", payload: demoProducts });
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  };

  const handleProductPress = (product: Product) => {
    router.push(`/product/${product.slug}`);
  };

  const handleCategoryPress = (category: Category) => {
    dispatch({ type: "SET_SELECTED_CATEGORY", payload: category.slug });
    router.push("/explore");
  };

  const handleAddToCart = (product: Product) => {
    if (product.customizable) {
      // Navigate to product detail for customization
      router.push(`/product/${product.slug}`);
    } else {
      // Add directly to cart
      addToCart(product, 1);
      Alert.alert("Success", `${product.name} added to cart!`);
    }
  };

  const handleToggleWishlist = (product: Product) => {
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  if (loading) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
            Starting up server and loading products...
          </Text>
          <Text
            style={[styles.loadingSubtext, { color: colors.text.secondary }]}
          >
            This may take a moment on first load
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header Section */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.headerLeft}>
              <Text style={[styles.greeting, { color: colors.text.secondary }]}>
                Welcome to
              </Text>
              <Text style={[styles.brandName, { color: colors.text.primary }]}>
                Six Light Media
              </Text>
            </View>

            {/* Header Actions */}
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={[
                  styles.headerButton,
                  { backgroundColor: colors.background.secondary },
                ]}
                onPress={() => router.push("/explore")}
              >
                <Text
                  style={[styles.searchIcon, { color: colors.text.primary }]}
                >
                  🔍
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.headerButton,
                  { backgroundColor: colors.background.secondary },
                ]}
                onPress={() => router.push("/(tabs)/cart")}
              >
                <Text style={[styles.cartIcon, { color: colors.text.primary }]}>
                  🛒
                </Text>
                {state.cartCount > 0 && (
                  <View
                    style={[
                      styles.cartBadge,
                      { backgroundColor: colors.primary[500] },
                    ]}
                  >
                    <Text style={styles.cartBadgeText}>{state.cartCount}</Text>
                  </View>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Error Banner */}
        {error && (
          <View style={styles.errorBanner}>
            <Card
              variant="flat"
              style={[
                styles.errorCard,
                { backgroundColor: colors.error + "20" },
              ]}
            >
              <View style={styles.errorContent}>
                <Text style={[styles.errorTitle, { color: colors.error }]}>
                  Connection Issue
                </Text>
                <Text
                  style={[
                    styles.errorMessage,
                    { color: colors.text.secondary },
                  ]}
                >
                  {error}. Showing demo content.
                </Text>
                <TouchableOpacity
                  style={[
                    styles.retryButton,
                    { backgroundColor: colors.primary[500] },
                  ]}
                  onPress={loadInitialData}
                >
                  <Text style={styles.retryButtonText}>Retry</Text>
                </TouchableOpacity>
              </View>
            </Card>
          </View>
        )}

        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Card variant="flat" padding="none" style={styles.heroCard}>
            <LinearGradient
              colors={[colors.primary[500], colors.primary[700]]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.heroGradient}
            >
              <View style={styles.heroContent}>
                <View style={styles.heroText}>
                  <Text style={styles.heroTitle}>Premium Custom Products</Text>
                  <Text style={styles.heroSubtitle}>
                    Transform your ideas into reality with our professional
                    printing and design services
                  </Text>
                  <View style={styles.heroButtons}>
                    <Button
                      title="Start Customizing"
                      onPress={() => router.push("/explore")}
                      variant="secondary"
                      style={styles.heroButton}
                    />
                    <TouchableOpacity
                      style={styles.heroSecondaryButton}
                      onPress={() => router.push("/orders")}
                    >
                      <Text style={styles.heroSecondaryButtonText}>
                        View Orders
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={styles.heroImageContainer}>
                  <View style={styles.heroImageBg}>
                    <Image
                      source={require("@/assets/images/6 Light Logo.png")}
                      style={styles.heroImage}
                      resizeMode="contain"
                    />
                  </View>
                </View>
              </View>
            </LinearGradient>
          </Card>
        </View>

        {/* Categories Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
              Shop by Category
            </Text>
            <TouchableOpacity onPress={() => router.push("/explore")}>
              <Text style={[styles.seeAllText, { color: colors.primary[500] }]}>
                See All
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {state.categories.map((category) => {
              const getCategoryIcon = (name: string) => {
                switch (name.toLowerCase()) {
                  case "apparel":
                    return "👕";
                  case "print":
                    return "🖨️";
                  case "digital":
                    return "💻";
                  case "business":
                    return "💼";
                  case "marketing":
                    return "📢";
                  default:
                    return "📦";
                }
              };

              return (
                <TouchableOpacity
                  key={category.id}
                  style={styles.categoryItem}
                  onPress={() => handleCategoryPress(category)}
                >
                  <Card
                    variant="elevated"
                    padding="md"
                    style={styles.categoryCard}
                  >
                    <View
                      style={[
                        styles.categoryIcon,
                        { backgroundColor: colors.primary[100] },
                      ]}
                    >
                      <Text style={styles.categoryEmoji}>
                        {getCategoryIcon(category.name)}
                      </Text>
                    </View>
                    <Text
                      style={[
                        styles.categoryName,
                        { color: colors.text.primary },
                      ]}
                    >
                      {category.name}
                    </Text>
                  </Card>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>

        {/* Quick Stats Section */}
        <View style={styles.section}>
          <View style={styles.statsContainer}>
            <Card variant="elevated" padding="md" style={styles.statCard}>
              <Text style={[styles.statNumber, { color: colors.primary[500] }]}>
                {state.products.length}+
              </Text>
              <Text
                style={[styles.statLabel, { color: colors.text.secondary }]}
              >
                Products
              </Text>
            </Card>
            <Card variant="elevated" padding="md" style={styles.statCard}>
              <Text style={[styles.statNumber, { color: colors.primary[500] }]}>
                {state.categories.length}+
              </Text>
              <Text
                style={[styles.statLabel, { color: colors.text.secondary }]}
              >
                Categories
              </Text>
            </Card>
            <Card variant="elevated" padding="md" style={styles.statCard}>
              <Text style={[styles.statNumber, { color: colors.primary[500] }]}>
                24/7
              </Text>
              <Text
                style={[styles.statLabel, { color: colors.text.secondary }]}
              >
                Support
              </Text>
            </Card>
          </View>
        </View>

        {/* Featured Products Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
              Featured Products
            </Text>
            <TouchableOpacity onPress={() => router.push("/explore")}>
              <Text style={[styles.seeAllText, { color: colors.primary[500] }]}>
                See All
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.productsContainer}
          >
            {state.featuredProducts.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                onPress={() => handleProductPress(product)}
                onAddToCart={() => handleAddToCart(product)}
                onToggleWishlist={() => handleToggleWishlist(product)}
                isInWishlist={isInWishlist(product.id)}
                style={styles.productCard}
              />
            ))}
          </ScrollView>
        </View>

        {/* Promotional Banner */}
        <View style={styles.section}>
          <View style={styles.promoSection}>
            <Card variant="elevated" padding="lg" style={styles.promoCard}>
              <View style={styles.promoContent}>
                <View style={styles.promoText}>
                  <Text
                    style={[styles.promoTitle, { color: colors.text.primary }]}
                  >
                    🎉 Special Offer
                  </Text>
                  <Text
                    style={[
                      styles.promoSubtitle,
                      { color: colors.text.secondary },
                    ]}
                  >
                    Get 20% off on your first custom order
                  </Text>
                  <Button
                    title="Claim Offer"
                    onPress={() => router.push("/explore")}
                    variant="primary"
                    size="sm"
                    style={styles.promoButton}
                  />
                </View>
                <View
                  style={[
                    styles.promoIcon,
                    { backgroundColor: colors.primary[100] },
                  ]}
                >
                  <Text style={styles.promoEmoji}>🎁</Text>
                </View>
              </View>
            </Card>
          </View>
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "500",
    marginBottom: Spacing.sm,
  },
  loadingSubtext: {
    fontSize: Typography.fontSize.base,
    opacity: 0.7,
    textAlign: "center",
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.md,
    paddingBottom: Spacing.lg,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    fontSize: Typography.fontSize.base,
    fontWeight: "400",
  },
  brandName: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.sm,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  searchIcon: {
    fontSize: 18,
  },
  cartIcon: {
    fontSize: 18,
  },
  cartBadge: {
    position: "absolute",
    top: -8,
    right: -8,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  cartBadgeText: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: "bold",
  },
  heroSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  heroCard: {
    borderRadius: BorderRadius.xl,
    overflow: "hidden",
  },
  heroGradient: {
    padding: Spacing.lg,
  },
  heroContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  heroText: {
    flex: 1,
    paddingRight: Spacing.lg,
  },
  heroTitle: {
    fontSize: Typography.fontSize["3xl"],
    fontWeight: "bold",
    color: Colors.white,
    marginBottom: Spacing.sm,
  },
  heroSubtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.white,
    opacity: 0.9,
    marginBottom: Spacing.lg,
    lineHeight: 24,
  },
  heroButtons: {
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.md,
    flexWrap: "wrap",
  },
  heroButton: {
    alignSelf: "flex-start",
  },
  heroSecondaryButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.white,
  },
  heroSecondaryButtonText: {
    color: Colors.white,
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  heroImageContainer: {
    width: 80,
    height: 80,
    justifyContent: "center",
    alignItems: "center",
  },
  heroImageBg: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  heroImage: {
    width: 50,
    height: 50,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  seeAllText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  categoriesContainer: {
    paddingLeft: Spacing.lg,
    paddingRight: Spacing.lg,
  },
  categoryItem: {
    marginRight: Spacing.md,
  },
  categoryCard: {
    width: 100,
    alignItems: "center",
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: Spacing.sm,
  },
  categoryEmoji: {
    fontSize: 24,
  },
  categoryName: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
    textAlign: "center",
  },
  // Stats Section
  statsContainer: {
    flexDirection: "row",
    paddingHorizontal: Spacing.lg,
    gap: Spacing.md,
  },
  statCard: {
    flex: 1,
    alignItems: "center",
    paddingVertical: Spacing.lg,
  },
  statNumber: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  statLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "500",
  },
  // Promo Section
  promoSection: {
    paddingHorizontal: Spacing.lg,
  },
  promoCard: {
    borderRadius: BorderRadius.lg,
  },
  promoContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  promoText: {
    flex: 1,
    paddingRight: Spacing.md,
  },
  promoTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  promoSubtitle: {
    fontSize: Typography.fontSize.base,
    marginBottom: Spacing.md,
    lineHeight: 22,
  },
  promoButton: {
    alignSelf: "flex-start",
  },
  promoIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
  },
  promoEmoji: {
    fontSize: 30,
  },
  productsContainer: {
    paddingLeft: Spacing.lg,
    paddingRight: Spacing.lg,
  },
  productCard: {
    marginRight: Spacing.md,
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
  errorBanner: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  errorCard: {
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
  },
  errorContent: {
    alignItems: "center",
  },
  errorTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  errorMessage: {
    fontSize: Typography.fontSize.sm,
    textAlign: "center",
    marginBottom: Spacing.md,
  },
  retryButton: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  retryButtonText: {
    color: Colors.white,
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
});
