/**
 * Six Light Media Store - Home Screen
 * Extraordinary home screen with modern e-commerce design
 */

import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  Dimensions,
  Image,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";

import Button from "@/components/ui/Button";
import Card, { ProductCard } from "@/components/ui/Card";
import { BorderRadius, Colors, Spacing, Typography } from "@/constants/Theme";
import { useApp } from "@/contexts/AppContext";
import { useTheme } from "@/contexts/ThemeContext";
import { apiClient, Category, Product } from "@/services/api";

const { width } = Dimensions.get("window");

export default function HomeScreen() {
  const { colors, isDark } = useTheme();
  const { state, dispatch, addToCart } = useApp();
  const router = useRouter();

  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError("");

      // Load products and categories in parallel
      const [products, categories] = await Promise.all([
        apiClient.getProducts({ limit: 20 }),
        apiClient.getCategories(),
      ]);

      dispatch({ type: "SET_PRODUCTS", payload: products });
      dispatch({ type: "SET_CATEGORIES", payload: categories });

      // Set featured products (first 6 products for now)
      dispatch({
        type: "SET_FEATURED_PRODUCTS",
        payload: products.slice(0, 6),
      });
    } catch (error) {
      console.error("Failed to load initial data:", error);

      // Set user-friendly error message
      const errorMessage =
        error instanceof Error ? error.message : "Unable to connect to server";
      setError(errorMessage);

      // Load fallback/demo data when API is unavailable
      loadFallbackData();
    } finally {
      setLoading(false);
    }
  };

  const loadFallbackData = () => {
    // Demo data to show when API is unavailable
    const demoProducts: Product[] = [
      {
        id: 1,
        name: "Custom T-Shirt",
        description: "High-quality custom printed t-shirt",
        price: 25.99,
        image: "https://via.placeholder.com/300x300?text=Custom+T-Shirt",
        category: "Apparel",
        customizable: true,
        slug: "custom-t-shirt",
      },
      {
        id: 2,
        name: "Business Cards",
        description: "Professional business cards",
        price: 15.99,
        image: "https://via.placeholder.com/300x300?text=Business+Cards",
        category: "Print",
        customizable: true,
        slug: "business-cards",
      },
      {
        id: 3,
        name: "Photo Print",
        description: "High-quality photo printing",
        price: 12.99,
        image: "https://via.placeholder.com/300x300?text=Photo+Print",
        category: "Print",
        customizable: false,
        slug: "photo-print",
      },
    ];

    const demoCategories: Category[] = [
      { id: 1, name: "Apparel" },
      { id: 2, name: "Print" },
      { id: 3, name: "Digital" },
    ];

    dispatch({ type: "SET_PRODUCTS", payload: demoProducts });
    dispatch({ type: "SET_CATEGORIES", payload: demoCategories });
    dispatch({ type: "SET_FEATURED_PRODUCTS", payload: demoProducts });
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  };

  const handleProductPress = (product: Product) => {
    router.push(`/product/${product.slug}`);
  };

  const handleCategoryPress = (category: Category) => {
    dispatch({ type: "SET_SELECTED_CATEGORY", payload: category.slug });
    router.push("/explore");
  };

  const handleAddToCart = (product: Product) => {
    if (product.customizable) {
      // Navigate to product detail for customization
      router.push(`/product/${product.slug}`);
    } else {
      // Add directly to cart
      addToCart(product, 1);
      Alert.alert("Success", `${product.name} added to cart!`);
    }
  };

  if (loading) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
            Loading amazing products...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header Section */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={[styles.greeting, { color: colors.text.secondary }]}>
                Welcome to
              </Text>
              <Text style={[styles.brandName, { color: colors.text.primary }]}>
                Six Light Media
              </Text>
            </View>

            {/* Profile/Cart Icons */}
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={[
                  styles.headerButton,
                  { backgroundColor: colors.background.secondary },
                ]}
                onPress={() => router.push("/(tabs)/cart")}
              >
                <View style={styles.cartIcon}>
                  {state.cartCount > 0 && (
                    <View
                      style={[
                        styles.cartBadge,
                        { backgroundColor: colors.error },
                      ]}
                    >
                      <Text style={styles.cartBadgeText}>
                        {state.cartCount}
                      </Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Error Banner */}
        {error && (
          <View style={styles.errorBanner}>
            <Card
              variant="flat"
              style={[
                styles.errorCard,
                { backgroundColor: colors.error + "20" },
              ]}
            >
              <View style={styles.errorContent}>
                <Text style={[styles.errorTitle, { color: colors.error }]}>
                  Connection Issue
                </Text>
                <Text
                  style={[
                    styles.errorMessage,
                    { color: colors.text.secondary },
                  ]}
                >
                  {error}. Showing demo content.
                </Text>
                <TouchableOpacity
                  style={[
                    styles.retryButton,
                    { backgroundColor: colors.primary[500] },
                  ]}
                  onPress={loadInitialData}
                >
                  <Text style={styles.retryButtonText}>Retry</Text>
                </TouchableOpacity>
              </View>
            </Card>
          </View>
        )}

        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Card variant="flat" padding="none" style={styles.heroCard}>
            <LinearGradient
              colors={[colors.primary[500], colors.primary[600]]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.heroGradient}
            >
              <View style={styles.heroContent}>
                <View style={styles.heroText}>
                  <Text style={styles.heroTitle}>Premium Custom Products</Text>
                  <Text style={styles.heroSubtitle}>
                    Design your perfect product with our advanced customization
                    tools
                  </Text>
                  <Button
                    title="Start Customizing"
                    onPress={() => router.push("/explore")}
                    variant="secondary"
                    style={styles.heroButton}
                  />
                </View>
                <View style={styles.heroImageContainer}>
                  <Image
                    source={require("@/assets/images/6 Light Logo.png")}
                    style={styles.heroImage}
                    resizeMode="contain"
                  />
                </View>
              </View>
            </LinearGradient>
          </Card>
        </View>

        {/* Categories Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
              Shop by Category
            </Text>
            <TouchableOpacity onPress={() => router.push("/explore")}>
              <Text style={[styles.seeAllText, { color: colors.primary[500] }]}>
                See All
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContainer}
          >
            {state.categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={styles.categoryItem}
                onPress={() => handleCategoryPress(category)}
              >
                <Card
                  variant="elevated"
                  padding="md"
                  style={styles.categoryCard}
                >
                  <View
                    style={[
                      styles.categoryIcon,
                      { backgroundColor: colors.primary[100] },
                    ]}
                  >
                    <View
                      style={[
                        styles.categoryIconInner,
                        { backgroundColor: colors.primary[500] },
                      ]}
                    />
                  </View>
                  <Text
                    style={[
                      styles.categoryName,
                      { color: colors.text.primary },
                    ]}
                  >
                    {category.name}
                  </Text>
                </Card>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Featured Products Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
              Featured Products
            </Text>
            <TouchableOpacity onPress={() => router.push("/explore")}>
              <Text style={[styles.seeAllText, { color: colors.primary[500] }]}>
                See All
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.productsContainer}
          >
            {state.featuredProducts.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                onPress={() => handleProductPress(product)}
                onAddToCart={() => handleAddToCart(product)}
                style={styles.productCard}
              />
            ))}
          </ScrollView>
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "500",
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingTop: Spacing.md,
    paddingBottom: Spacing.lg,
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  greeting: {
    fontSize: Typography.fontSize.base,
    fontWeight: "400",
  },
  brandName: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  cartIcon: {
    width: 24,
    height: 24,
    backgroundColor: Colors.secondary[400],
    borderRadius: 4,
  },
  cartBadge: {
    position: "absolute",
    top: -8,
    right: -8,
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  cartBadgeText: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: "bold",
  },
  heroSection: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.xl,
  },
  heroCard: {
    borderRadius: BorderRadius.xl,
    overflow: "hidden",
  },
  heroGradient: {
    padding: Spacing.lg,
  },
  heroContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  heroText: {
    flex: 1,
    paddingRight: Spacing.lg,
  },
  heroTitle: {
    fontSize: Typography.fontSize["3xl"],
    fontWeight: "bold",
    color: Colors.white,
    marginBottom: Spacing.sm,
  },
  heroSubtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.white,
    opacity: 0.9,
    marginBottom: Spacing.lg,
    lineHeight: 24,
  },
  heroButton: {
    alignSelf: "flex-start",
  },
  heroImageContainer: {
    width: 80,
    height: 80,
    justifyContent: "center",
    alignItems: "center",
  },
  heroImage: {
    width: 60,
    height: 60,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  seeAllText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  categoriesContainer: {
    paddingLeft: Spacing.lg,
    paddingRight: Spacing.lg,
  },
  categoryItem: {
    marginRight: Spacing.md,
  },
  categoryCard: {
    width: 100,
    alignItems: "center",
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: Spacing.sm,
  },
  categoryIconInner: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  categoryName: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
    textAlign: "center",
  },
  productsContainer: {
    paddingLeft: Spacing.lg,
    paddingRight: Spacing.lg,
  },
  productCard: {
    marginRight: Spacing.md,
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
  errorBanner: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  errorCard: {
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
  },
  errorContent: {
    alignItems: "center",
  },
  errorTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  errorMessage: {
    fontSize: Typography.fontSize.sm,
    textAlign: "center",
    marginBottom: Spacing.md,
  },
  retryButton: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  retryButtonText: {
    color: Colors.white,
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
});
