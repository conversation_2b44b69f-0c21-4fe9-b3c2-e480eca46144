/**
 * Six Light Media Store - Profile Screen
 * User profile, settings, and account management
 */

import React from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";

interface MenuItemProps {
  title: string;
  subtitle?: string;
  onPress: () => void;
  showArrow?: boolean;
  textColor?: string;
}

function MenuItem({
  title,
  subtitle,
  onPress,
  showArrow = true,
  textColor,
}: MenuItemProps) {
  const { colors } = useTheme();

  return (
    <TouchableOpacity onPress={onPress}>
      <Card variant="flat" padding="md" style={styles.menuItem}>
        <View style={styles.menuItemContent}>
          <View style={styles.menuItemText}>
            <Text
              style={[
                styles.menuItemTitle,
                { color: textColor || colors.text.primary },
              ]}
            >
              {title}
            </Text>
            {subtitle && (
              <Text
                style={[
                  styles.menuItemSubtitle,
                  { color: colors.text.secondary },
                ]}
              >
                {subtitle}
              </Text>
            )}
          </View>
          {showArrow && (
            <View
              style={[
                styles.menuItemArrow,
                { backgroundColor: colors.secondary[400] },
              ]}
            />
          )}
        </View>
      </Card>
    </TouchableOpacity>
  );
}

export default function ProfileScreen() {
  const { colors, themeMode, setThemeMode } = useTheme();
  const { state, logout } = useApp();
  const router = useRouter();

  const handleLogin = () => {
    router.push("/auth/login");
  };

  const handleRegister = () => {
    router.push("/auth/register");
  };

  const handleLogout = () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Logout",
        style: "destructive",
        onPress: logout,
      },
    ]);
  };

  const handleThemeChange = () => {
    const modes: Array<"light" | "dark" | "system"> = [
      "light",
      "dark",
      "system",
    ];
    const currentIndex = modes.indexOf(themeMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setThemeMode(modes[nextIndex]);
  };

  const getThemeDisplayName = () => {
    switch (themeMode) {
      case "light":
        return "Light";
      case "dark":
        return "Dark";
      case "system":
        return "System";
      default:
        return "System";
    }
  };

  if (!state.isAuthenticated) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
            Profile
          </Text>
        </View>

        <View style={styles.guestContainer}>
          <View
            style={[
              styles.guestIcon,
              { backgroundColor: colors.secondary[200] },
            ]}
          />
          <Text style={[styles.guestTitle, { color: colors.text.primary }]}>
            Welcome to Six Light Media
          </Text>
          <Text
            style={[styles.guestSubtitle, { color: colors.text.secondary }]}
          >
            Sign in to access your profile, orders, and personalized
            recommendations
          </Text>

          <View style={styles.authButtons}>
            <Button
              title="Sign In"
              onPress={handleLogin}
              variant="primary"
              size="lg"
              fullWidth
              style={styles.authButton}
            />
            <Button
              title="Create Account"
              onPress={handleRegister}
              variant="outline"
              size="lg"
              fullWidth
              style={styles.authButton}
            />
          </View>
        </View>

        {/* Guest Menu Items */}
        <View style={styles.menuSection}>
          <MenuItem
            title="Theme"
            subtitle={`Currently: ${getThemeDisplayName()}`}
            onPress={handleThemeChange}
          />
          <MenuItem title="About Six Light Media" onPress={() => {}} />
          <MenuItem title="Contact Support" onPress={() => {}} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
            Profile
          </Text>
        </View>

        {/* User Info */}
        <Card variant="elevated" padding="lg" style={styles.userCard}>
          <View style={styles.userInfo}>
            <View
              style={[styles.avatar, { backgroundColor: colors.primary[500] }]}
            >
              <Text style={styles.avatarText}>
                {state.user?.name?.charAt(0).toUpperCase() || "U"}
              </Text>
            </View>
            <View style={styles.userDetails}>
              <Text style={[styles.userName, { color: colors.text.primary }]}>
                {state.user?.name || "User"}
              </Text>
              <Text
                style={[styles.userEmail, { color: colors.text.secondary }]}
              >
                {state.user?.email || "<EMAIL>"}
              </Text>
            </View>
          </View>
        </Card>

        {/* Account Section */}
        <View style={styles.menuSection}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Account
          </Text>
          <MenuItem
            title="My Orders"
            subtitle="View your order history"
            onPress={() => router.push("/orders")}
          />
          <MenuItem
            title="Edit Profile"
            subtitle="Update your personal information"
            onPress={() => router.push("/profile/edit")}
          />
          <MenuItem
            title="Addresses"
            subtitle="Manage shipping addresses"
            onPress={() => router.push("/profile/addresses")}
          />
        </View>

        {/* Admin Section (only for admin users) */}
        {state.user?.role === "ADMIN" && (
          <View style={styles.menuSection}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
              Admin Panel
            </Text>
            <MenuItem
              title="Admin Dashboard"
              subtitle="Overview and analytics"
              onPress={() => router.push("/admin/dashboard")}
            />
            <MenuItem
              title="Manage Products"
              subtitle="Add, edit, and delete products"
              onPress={() => router.push("/admin/products")}
            />
            <MenuItem
              title="Manage Orders"
              subtitle="View and update order status"
              onPress={() => router.push("/admin/orders")}
            />
          </View>
        )}

        {/* Preferences Section */}
        <View style={styles.menuSection}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Preferences
          </Text>
          <MenuItem
            title="Theme"
            subtitle={`Currently: ${getThemeDisplayName()}`}
            onPress={handleThemeChange}
          />
          <MenuItem
            title="Notifications"
            subtitle="Manage your notification preferences"
            onPress={() => router.push("/profile/notifications")}
          />
        </View>

        {/* Support Section */}
        <View style={styles.menuSection}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Support
          </Text>
          <MenuItem title="Help Center" onPress={() => {}} />
          <MenuItem title="Contact Support" onPress={() => {}} />
          <MenuItem title="About Six Light Media" onPress={() => {}} />
          {__DEV__ && (
            <MenuItem
              title="🔧 Debug Configuration"
              subtitle="View environment variables and config"
              onPress={() => router.push("/debug/config")}
            />
          )}
        </View>

        {/* Logout */}
        <View style={styles.menuSection}>
          <MenuItem
            title="Logout"
            onPress={handleLogout}
            showArrow={false}
            textColor={colors.error}
          />
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  headerTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
  },
  guestContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing["3xl"],
  },
  guestIcon: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: Spacing.xl,
  },
  guestTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    marginBottom: Spacing.sm,
    textAlign: "center",
  },
  guestSubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.xl,
    lineHeight: 24,
  },
  authButtons: {
    width: "100%",
    gap: Spacing.md,
  },
  authButton: {
    marginBottom: 0,
  },
  userCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.md,
  },
  avatarText: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    color: Colors.white,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontSize: Typography.fontSize.base,
  },
  menuSection: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  menuItem: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.xs,
  },
  menuItemContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  menuItemText: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  menuItemSubtitle: {
    fontSize: Typography.fontSize.sm,
  },
  menuItemArrow: {
    width: 20,
    height: 20,
    borderRadius: 4,
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
});
