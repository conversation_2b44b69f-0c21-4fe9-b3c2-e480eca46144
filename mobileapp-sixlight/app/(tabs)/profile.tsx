/**
 * Six Light Media Store - Profile Screen
 * User profile, settings, and account management
 */

import React from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Image,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { apiClient } from "@/services/api";

interface MenuItemProps {
  title: string;
  subtitle?: string;
  onPress: () => void;
  showArrow?: boolean;
  textColor?: string;
  icon?: string;
  rightElement?: React.ReactNode;
}

function MenuItem({
  title,
  subtitle,
  onPress,
  showArrow = true,
  textColor,
  icon,
  rightElement,
}: MenuItemProps) {
  const { colors } = useTheme();

  return (
    <TouchableOpacity onPress={onPress}>
      <Card variant="flat" padding="md" style={styles.menuItem}>
        <View style={styles.menuItemContent}>
          {icon && (
            <View style={styles.menuItemIcon}>
              <Text style={styles.menuItemIconText}>{icon}</Text>
            </View>
          )}
          <View style={styles.menuItemText}>
            <Text
              style={[
                styles.menuItemTitle,
                { color: textColor || colors.text.primary },
              ]}
            >
              {title}
            </Text>
            {subtitle && (
              <Text
                style={[
                  styles.menuItemSubtitle,
                  { color: colors.text.secondary },
                ]}
              >
                {subtitle}
              </Text>
            )}
          </View>
          <View style={styles.menuItemRight}>
            {rightElement}
            {showArrow && !rightElement && (
              <Text
                style={[styles.menuItemArrow, { color: colors.text.secondary }]}
              >
                →
              </Text>
            )}
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );
}

export default function ProfileScreen() {
  const { colors, themeMode, setThemeMode } = useTheme();
  const { state, logout } = useApp();
  const router = useRouter();

  const handleLogin = () => {
    router.push("/auth/login");
  };

  const handleRegister = () => {
    router.push("/auth/register");
  };

  const handleLogout = () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Logout",
        style: "destructive",
        onPress: logout,
      },
    ]);
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      "⚠️ Delete Account",
      "This action cannot be undone. All your data, orders, and account information will be permanently deleted.\n\nWhat will be deleted:\n• Your profile and personal information\n• All your orders and order history\n• Your preferences and settings\n• Any saved addresses",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Continue",
          style: "destructive",
          onPress: confirmDeleteAccount,
        },
      ]
    );
  };

  const confirmDeleteAccount = () => {
    Alert.alert(
      "🚨 Final Confirmation",
      `Are you absolutely sure you want to delete your account?\n\nAccount: ${state.user?.email}\nName: ${state.user?.name}\n\nThis action is irreversible and you will lose all your data permanently.`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Yes, Delete Forever",
          style: "destructive",
          onPress: executeDeleteAccount,
        },
      ]
    );
  };

  const executeDeleteAccount = async () => {
    try {
      // Show loading state
      Alert.alert(
        "Deleting Account",
        "Please wait while we delete your account...",
        [],
        {
          cancelable: false,
        }
      );

      const response = await apiClient.deleteAccount();

      if (response.success) {
        Alert.alert(
          "✅ Account Deleted",
          "Your account has been successfully deleted. Thank you for using Six Light Media.\n\nWe're sorry to see you go. If you have any feedback about your experience, please contact our support team.",
          [
            {
              text: "OK",
              onPress: async () => {
                await logout();
                router.replace("/");
              },
            },
          ]
        );
      } else {
        throw new Error("Account deletion failed");
      }
    } catch (error) {
      console.error("Failed to delete account:", error);

      let errorMessage =
        "Failed to delete account. Please try again or contact support.";

      if (error instanceof Error) {
        if (
          error.message.includes("unauthorized") ||
          error.message.includes("401")
        ) {
          errorMessage =
            "Your session has expired. Please log in again and try deleting your account.";
        } else if (
          error.message.includes("network") ||
          error.message.includes("timeout")
        ) {
          errorMessage =
            "Network error. Please check your connection and try again.";
        } else {
          errorMessage = error.message;
        }
      }

      Alert.alert("❌ Delete Failed", errorMessage, [
        { text: "Try Again", onPress: executeDeleteAccount },
        { text: "Cancel", style: "cancel" },
      ]);
    }
  };

  const getThemeDisplayName = () => {
    switch (themeMode) {
      case "light":
        return "Light";
      case "dark":
        return "Dark";
      case "system":
        return "System";
      default:
        return "System";
    }
  };

  const getThemeIcon = () => {
    switch (themeMode) {
      case "light":
        return "☀️";
      case "dark":
        return "🌙";
      case "system":
        return "📱";
      default:
        return "📱";
    }
  };

  const ThemeToggle = () => {
    const { colors } = useTheme();

    return (
      <View style={styles.themeToggleContainer}>
        <TouchableOpacity
          style={[
            styles.themeOption,
            {
              backgroundColor:
                themeMode === "light"
                  ? colors.primary[500]
                  : colors.background.secondary,
            },
          ]}
          onPress={() => setThemeMode("light")}
        >
          <Text
            style={[
              styles.themeOptionIcon,
              {
                color:
                  themeMode === "light" ? Colors.white : colors.text.secondary,
              },
            ]}
          >
            ☀️
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.themeOption,
            {
              backgroundColor:
                themeMode === "dark"
                  ? colors.primary[500]
                  : colors.background.secondary,
            },
          ]}
          onPress={() => setThemeMode("dark")}
        >
          <Text
            style={[
              styles.themeOptionIcon,
              {
                color:
                  themeMode === "dark" ? Colors.white : colors.text.secondary,
              },
            ]}
          >
            🌙
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.themeOption,
            {
              backgroundColor:
                themeMode === "system"
                  ? colors.primary[500]
                  : colors.background.secondary,
            },
          ]}
          onPress={() => setThemeMode("system")}
        >
          <Text
            style={[
              styles.themeOptionIcon,
              {
                color:
                  themeMode === "system" ? Colors.white : colors.text.secondary,
              },
            ]}
          >
            📱
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (!state.isAuthenticated) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
            Profile
          </Text>
        </View>

        <View style={styles.guestContainer}>
          <View
            style={[
              styles.guestIcon,
              { backgroundColor: colors.secondary[200] },
            ]}
          >
            <Text
              style={[styles.guestUserIcon, { color: colors.secondary[500] }]}
            >
              👤
            </Text>
          </View>
          <Text style={[styles.guestTitle, { color: colors.text.primary }]}>
            Welcome to Six Light Media
          </Text>
          <Text
            style={[styles.guestSubtitle, { color: colors.text.secondary }]}
          >
            Sign in to access your profile, orders, and personalized
            recommendations
          </Text>

          <View style={styles.authButtons}>
            <Button
              title="Sign In"
              onPress={handleLogin}
              variant="primary"
              size="lg"
              fullWidth
              style={styles.authButton}
            />
            <Button
              title="Create Account"
              onPress={handleRegister}
              variant="outline"
              size="lg"
              fullWidth
              style={styles.authButton}
            />
          </View>
        </View>

        {/* Guest Menu Items */}
        <View style={styles.menuSection}>
          <MenuItem
            icon={getThemeIcon()}
            title="Theme"
            subtitle={`Currently: ${getThemeDisplayName()}`}
            onPress={() => {}}
            rightElement={<ThemeToggle />}
            showArrow={false}
          />
          <MenuItem
            icon="ℹ️"
            title="About Six Light Media"
            onPress={() => {}}
          />
          <MenuItem icon="💬" title="Contact Support" onPress={() => {}} />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
            Profile
          </Text>
        </View>

        {/* User Info */}
        <Card variant="elevated" padding="lg" style={styles.userCard}>
          <View style={styles.userInfo}>
            <View
              style={[
                styles.avatar,
                {
                  backgroundColor: state.user?.profileImage
                    ? "transparent"
                    : colors.secondary[200],
                },
              ]}
            >
              {state.user?.profileImage ? (
                <Image
                  source={{ uri: state.user.profileImage }}
                  style={styles.avatarImage}
                />
              ) : (
                <Text
                  style={[styles.userIcon, { color: colors.secondary[500] }]}
                >
                  👤
                </Text>
              )}
            </View>
            <View style={styles.userDetails}>
              <Text style={[styles.userName, { color: colors.text.primary }]}>
                {state.user?.name || "User"}
              </Text>
              <Text
                style={[styles.userEmail, { color: colors.text.secondary }]}
              >
                {state.user?.email || "<EMAIL>"}
              </Text>
              <View style={styles.badgeContainer}>
                <View
                  style={[
                    styles.memberBadge,
                    { backgroundColor: colors.primary[100] },
                  ]}
                >
                  <Text
                    style={[
                      styles.memberBadgeText,
                      { color: colors.primary[700] },
                    ]}
                  >
                    {state.user?.role === "ADMIN"
                      ? "👑 Admin"
                      : "⭐ Premium Member"}
                  </Text>
                </View>
                <View
                  style={[
                    styles.themeBadge,
                    { backgroundColor: colors.secondary[100] },
                  ]}
                >
                  <Text
                    style={[
                      styles.themeBadgeText,
                      { color: colors.secondary[700] },
                    ]}
                  >
                    {getThemeIcon()} {getThemeDisplayName()}
                  </Text>
                </View>
              </View>
            </View>
            <TouchableOpacity
              style={[
                styles.editProfileButton,
                { backgroundColor: colors.primary[500] },
              ]}
              onPress={() => router.push("/profile/edit")}
            >
              <Text style={styles.editProfileButtonText}>Edit</Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Account Section */}
        <View style={styles.menuSection}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Account
          </Text>
          <MenuItem
            icon="📊"
            title="Dashboard"
            subtitle="View your account overview"
            onPress={() => router.push("/dashboard")}
          />
          <MenuItem
            icon="📦"
            title="My Orders"
            subtitle="View your order history"
            onPress={() => router.push("/orders")}
          />
          <MenuItem
            icon="✏️"
            title="Edit Profile"
            subtitle="Update your personal information"
            onPress={() => router.push("/profile/edit")}
          />
          <MenuItem
            icon="📍"
            title="Addresses"
            subtitle="Manage shipping addresses"
            onPress={() => router.push("/profile/addresses")}
          />
        </View>

        {/* Admin Section (only for admin users) */}
        {state.user?.role === "ADMIN" && (
          <View style={styles.menuSection}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
              ⚡ Admin Panel
            </Text>
            <MenuItem
              icon="🎛️"
              title="Admin Dashboard"
              subtitle="Overview and analytics"
              onPress={() => router.push("/admin/dashboard")}
            />
            <MenuItem
              icon="📦"
              title="Manage Products"
              subtitle="Add, edit, and delete products"
              onPress={() => router.push("/admin/products")}
            />
            <MenuItem
              icon="📋"
              title="Manage Orders"
              subtitle="View and update order status"
              onPress={() => router.push("/admin/orders")}
            />
          </View>
        )}

        {/* Preferences Section */}
        <View style={styles.menuSection}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            ⚙️ Preferences
          </Text>
          <MenuItem
            icon={getThemeIcon()}
            title="Theme"
            subtitle={`Currently: ${getThemeDisplayName()}`}
            onPress={() => {}}
            rightElement={<ThemeToggle />}
            showArrow={false}
          />
          <MenuItem
            icon="🔔"
            title="Notifications"
            subtitle="Manage your notification preferences"
            onPress={() => router.push("/profile/notifications")}
          />
        </View>

        {/* Support Section */}
        <View style={styles.menuSection}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            🆘 Support
          </Text>
          <MenuItem icon="❓" title="Help Center" onPress={() => {}} />
          <MenuItem icon="💬" title="Contact Support" onPress={() => {}} />
          <MenuItem
            icon="ℹ️"
            title="About Six Light Media"
            onPress={() => {}}
          />
          {__DEV__ && (
            <MenuItem
              icon="🔧"
              title="Debug Configuration"
              subtitle="View environment variables and config"
              onPress={() => router.push("/debug/config")}
            />
          )}
        </View>

        {/* Danger Zone */}
        <View style={styles.menuSection}>
          <Text style={[styles.sectionTitle, { color: colors.error }]}>
            ⚠️ Danger Zone
          </Text>
          <MenuItem
            icon="🚪"
            title="Logout"
            onPress={handleLogout}
            showArrow={false}
            textColor={colors.error}
          />
          <MenuItem
            icon="🗑️"
            title="Delete Account"
            subtitle="Permanently delete your account and all data"
            onPress={handleDeleteAccount}
            showArrow={false}
            textColor={colors.error}
          />
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  headerTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
  },
  guestContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing["3xl"],
  },
  guestIcon: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: Spacing.xl,
    justifyContent: "center",
    alignItems: "center",
  },
  guestUserIcon: {
    fontSize: 50,
  },
  guestTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    marginBottom: Spacing.sm,
    textAlign: "center",
  },
  guestSubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.xl,
    lineHeight: 24,
  },
  authButtons: {
    width: "100%",
    gap: Spacing.md,
  },
  authButton: {
    marginBottom: 0,
  },
  userCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.md,
  },
  avatarText: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    color: Colors.white,
  },
  avatarImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  userIcon: {
    fontSize: 30,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontSize: Typography.fontSize.base,
    marginBottom: Spacing.sm,
  },
  badgeContainer: {
    flexDirection: "row",
    gap: Spacing.sm,
    flexWrap: "wrap",
  },
  memberBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
    borderRadius: BorderRadius.sm,
    alignSelf: "flex-start",
  },
  memberBadgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: "600",
  },
  themeBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
    borderRadius: BorderRadius.sm,
    alignSelf: "flex-start",
  },
  themeBadgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: "600",
  },
  editProfileButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    justifyContent: "center",
    alignItems: "center",
  },
  editProfileButtonText: {
    color: Colors.white,
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  menuSection: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.md,
    paddingHorizontal: Spacing.lg,
  },
  menuItem: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.xs,
  },
  menuItemContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  menuItemIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.secondary[100],
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.md,
  },
  menuItemIconText: {
    fontSize: 16,
  },
  menuItemText: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  menuItemSubtitle: {
    fontSize: Typography.fontSize.sm,
  },
  menuItemRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.sm,
  },
  menuItemArrow: {
    fontSize: 16,
    fontWeight: "600",
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
  themeToggleContainer: {
    flexDirection: "row",
    gap: Spacing.sm,
  },
  themeOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: Colors.secondary[300],
  },
  themeOptionIcon: {
    fontSize: 18,
  },
});
