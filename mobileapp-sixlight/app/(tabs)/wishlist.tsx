/**
 * Six Light Media Store - Wishlist Screen
 * User's favorite products and wishlist management
 */

import React from "react";
import { View, Text, StyleSheet, FlatList, Dimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import { ProductCard } from "@/components/ui/Card";
import { Product } from "@/services/api";

const { width } = Dimensions.get("window");
const PRODUCT_CARD_WIDTH = (width - Spacing.lg * 3) / 2;

export default function WishlistScreen() {
  const { colors } = useTheme();
  const { state, addToCart, removeFromWishlist, isInWishlist } = useApp();
  const router = useRouter();

  const handleProductPress = (product: Product) => {
    router.push(`/product/${product.slug}`);
  };

  const handleAddToCart = (product: Product) => {
    addToCart(product);
    // Optionally show a toast notification
    console.log("Added to cart:", product.name);
  };

  const handleToggleWishlist = (product: Product) => {
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    }
  };

  const renderWishlistItem = ({ item }: { item: Product }) => (
    <ProductCard
      product={item}
      onPress={() => handleProductPress(item)}
      onAddToCart={() => handleAddToCart(item)}
      onToggleWishlist={() => handleToggleWishlist(item)}
      isInWishlist={true}
      style={[styles.productCard, { width: PRODUCT_CARD_WIDTH }]}
    />
  );

  if (state.wishlist.length === 0) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
            Wishlist
          </Text>
        </View>

        <View style={styles.emptyContainer}>
          <View
            style={[
              styles.emptyIcon,
              { backgroundColor: colors.secondary[200] },
            ]}
          >
            <Text style={styles.emptyIconText}>💝</Text>
          </View>
          <Text style={[styles.emptyTitle, { color: colors.text.primary }]}>
            Your wishlist is empty
          </Text>
          <Text
            style={[styles.emptySubtitle, { color: colors.text.secondary }]}
          >
            Save your favorite products to your wishlist so you can easily find
            them later
          </Text>
          <Button
            title="❤️ Explore Products"
            onPress={() => router.push("/explore")}
            variant="primary"
            style={styles.exploreButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          Wishlist
        </Text>
        <Text style={[styles.itemCount, { color: colors.text.secondary }]}>
          {state.wishlist.length} item{state.wishlist.length !== 1 ? "s" : ""}
        </Text>
      </View>

      {/* Wishlist Items */}
      <FlatList
        data={state.wishlist}
        renderItem={renderWishlistItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        contentContainerStyle={styles.wishlistGrid}
        columnWrapperStyle={styles.productRow}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
  },
  itemCount: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  wishlistGrid: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing["3xl"],
  },
  productRow: {
    justifyContent: "space-between",
  },
  productCard: {
    marginBottom: Spacing.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
  },
  emptyIcon: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: Spacing.xl,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyIconText: {
    fontSize: 48,
  },
  emptyTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    marginBottom: Spacing.sm,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.xl,
    lineHeight: 24,
  },
  exploreButton: {
    paddingHorizontal: Spacing.xl,
  },
});
