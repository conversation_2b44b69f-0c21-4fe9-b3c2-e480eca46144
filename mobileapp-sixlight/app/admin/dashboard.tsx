/**
 * Six Light Media Store - Mobile Admin Dashboard
 * Complete admin functionality for mobile app
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { apiClient } from "@/services/api";

interface DashboardStats {
  users: number;
  orders: number;
  products: number;
  collectedOrders: number;
}

interface DashboardData {
  stats: DashboardStats;
  recentOrders: any[];
  recentUsers: any[];
  products: any[];
}

export default function AdminDashboardScreen() {
  const { colors } = useTheme();
  const { state } = useApp();
  const router = useRouter();

  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    checkAdminAccess();
    loadDashboardData();
  }, []);

  const checkAdminAccess = () => {
    if (!state.isAuthenticated) {
      Alert.alert(
        "Access Denied",
        "You must be logged in to access the admin dashboard.",
        [
          { text: "Login", onPress: () => router.replace("/auth/login") },
          { text: "Cancel", onPress: () => router.back() },
        ]
      );
      return;
    }

    if (state.user?.role !== "ADMIN") {
      Alert.alert(
        "Access Denied",
        "You do not have admin privileges to access this area.",
        [{ text: "OK", onPress: () => router.back() }]
      );
      return;
    }
  };

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError("");

      // In a real implementation, you would have a dedicated admin dashboard endpoint
      // For now, we'll simulate the data structure
      const mockData: DashboardData = {
        stats: {
          users: 0,
          orders: 0,
          products: 0,
          collectedOrders: 0,
        },
        recentOrders: [],
        recentUsers: [],
        products: [],
      };

      setData(mockData);
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
      setError("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const navigateToSection = (section: string) => {
    router.push(`/admin/${section}` as any);
  };

  if (loading && !data) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
            Loading admin dashboard...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error && !data) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.errorContainer}>
          <Text style={[styles.errorTitle, { color: colors.text.primary }]}>
            Error Loading Dashboard
          </Text>
          <Text style={[styles.errorText, { color: colors.text.secondary }]}>
            {error}
          </Text>
          <Button
            title="Retry"
            onPress={loadDashboardData}
            variant="primary"
            style={styles.retryButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Text style={[styles.backButtonText, { color: colors.text.primary }]}>
            ← Back
          </Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          Admin Dashboard
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Welcome Section */}
        <Card variant="elevated" padding="lg" style={styles.welcomeCard}>
          <Text style={[styles.welcomeTitle, { color: colors.text.primary }]}>
            Welcome, Admin! 👋
          </Text>
          <Text
            style={[styles.welcomeSubtitle, { color: colors.text.secondary }]}
          >
            Manage your Six Light Media store from your mobile device
          </Text>
        </Card>

        {/* Stats Overview */}
        <Card variant="elevated" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            📊 Overview
          </Text>
          <View style={styles.statsGrid}>
            <View
              style={[
                styles.statCard,
                { backgroundColor: colors.primary[100] },
              ]}
            >
              <Text style={[styles.statNumber, { color: colors.primary[700] }]}>
                {data?.stats.users || 0}
              </Text>
              <Text style={[styles.statLabel, { color: colors.primary[600] }]}>
                Users
              </Text>
            </View>
            <View
              style={[
                styles.statCard,
                { backgroundColor: colors.secondary[100] },
              ]}
            >
              <Text
                style={[styles.statNumber, { color: colors.secondary[700] }]}
              >
                {data?.stats.orders || 0}
              </Text>
              <Text
                style={[styles.statLabel, { color: colors.secondary[600] }]}
              >
                Orders
              </Text>
            </View>
            <View
              style={[
                styles.statCard,
                { backgroundColor: colors.success[100] },
              ]}
            >
              <Text style={[styles.statNumber, { color: colors.success[700] }]}>
                {data?.stats.products || 0}
              </Text>
              <Text style={[styles.statLabel, { color: colors.success[600] }]}>
                Products
              </Text>
            </View>
            <View
              style={[
                styles.statCard,
                { backgroundColor: colors.warning[100] },
              ]}
            >
              <Text style={[styles.statNumber, { color: colors.warning[700] }]}>
                {data?.stats.collectedOrders || 0}
              </Text>
              <Text style={[styles.statLabel, { color: colors.warning[600] }]}>
                Collected
              </Text>
            </View>
          </View>
        </Card>

        {/* Quick Actions */}
        <Card variant="elevated" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            🚀 Quick Actions
          </Text>
          <View style={styles.actionsGrid}>
            <TouchableOpacity
              style={[
                styles.actionCard,
                { backgroundColor: colors.primary[500] },
              ]}
              onPress={() => navigateToSection("products")}
            >
              <Text style={styles.actionIcon}>📦</Text>
              <Text style={styles.actionTitle}>Manage Products</Text>
              <Text style={styles.actionSubtitle}>
                Add, edit, delete products
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.actionCard,
                { backgroundColor: colors.secondary[500] },
              ]}
              onPress={() => navigateToSection("categories")}
            >
              <Text style={styles.actionIcon}>🏷️</Text>
              <Text style={styles.actionTitle}>Categories</Text>
              <Text style={styles.actionSubtitle}>
                Organize product categories
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.actionCard,
                { backgroundColor: colors.success[500] },
              ]}
              onPress={() => navigateToSection("orders")}
            >
              <Text style={styles.actionIcon}>📋</Text>
              <Text style={styles.actionTitle}>Orders</Text>
              <Text style={styles.actionSubtitle}>View and manage orders</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.actionCard,
                { backgroundColor: colors.warning[500] },
              ]}
              onPress={() => navigateToSection("users")}
            >
              <Text style={styles.actionIcon}>👥</Text>
              <Text style={styles.actionTitle}>Users</Text>
              <Text style={styles.actionSubtitle}>Manage user accounts</Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Performance Metrics */}
        <Card variant="elevated" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            📈 Performance
          </Text>
          <View style={styles.metricsContainer}>
            <View
              style={[
                styles.metricCard,
                { backgroundColor: colors.background.secondary },
              ]}
            >
              <Text
                style={[styles.metricLabel, { color: colors.text.secondary }]}
              >
                Completion Rate
              </Text>
              <Text
                style={[styles.metricValue, { color: colors.primary[500] }]}
              >
                {data?.stats.orders && data?.stats.collectedOrders
                  ? `${Math.round(
                      (data.stats.collectedOrders / data.stats.orders) * 100
                    )}%`
                  : "0%"}
              </Text>
            </View>
            <View
              style={[
                styles.metricCard,
                { backgroundColor: colors.background.secondary },
              ]}
            >
              <Text
                style={[styles.metricLabel, { color: colors.text.secondary }]}
              >
                Pending Orders
              </Text>
              <Text
                style={[styles.metricValue, { color: colors.warning[500] }]}
              >
                {data?.stats.orders && data?.stats.collectedOrders
                  ? data.stats.orders - data.stats.collectedOrders
                  : 0}
              </Text>
            </View>
          </View>
        </Card>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  backButton: {
    padding: Spacing.sm,
  },
  backButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  placeholder: {
    width: 60,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  welcomeCard: {
    marginTop: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  welcomeTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    marginBottom: Spacing.sm,
  },
  welcomeSubtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: 24,
  },
  section: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.md,
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.md,
  },
  statCard: {
    flex: 1,
    minWidth: "45%",
    padding: Spacing.md,
    borderRadius: BorderRadius.lg,
    alignItems: "center",
  },
  statNumber: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  statLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  actionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.md,
  },
  actionCard: {
    flex: 1,
    minWidth: "45%",
    padding: Spacing.lg,
    borderRadius: BorderRadius.xl,
    alignItems: "center",
  },
  actionIcon: {
    fontSize: 32,
    marginBottom: Spacing.sm,
  },
  actionTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: "bold",
    color: Colors.white,
    marginBottom: Spacing.xs,
    textAlign: "center",
  },
  actionSubtitle: {
    fontSize: Typography.fontSize.sm,
    color: Colors.white,
    opacity: 0.9,
    textAlign: "center",
  },
  metricsContainer: {
    flexDirection: "row",
    gap: Spacing.md,
  },
  metricCard: {
    flex: 1,
    padding: Spacing.md,
    borderRadius: BorderRadius.lg,
    alignItems: "center",
  },
  metricLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "500",
    marginBottom: Spacing.xs,
    textAlign: "center",
  },
  metricValue: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "500",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
  },
  errorTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    marginBottom: Spacing.sm,
    textAlign: "center",
  },
  errorText: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.lg,
    lineHeight: 24,
  },
  retryButton: {
    paddingHorizontal: Spacing.xl,
  },
});
