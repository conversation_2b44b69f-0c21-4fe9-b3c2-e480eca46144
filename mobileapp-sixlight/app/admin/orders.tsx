/**
 * Six Light Media Store - Mobile Admin Orders Management
 * Complete order management for mobile admin
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { apiClient, Order, formatPrice } from "@/services/api";

export default function AdminOrdersScreen() {
  const { colors } = useTheme();
  const { state } = useApp();
  const router = useRouter();

  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<"ALL" | "PENDING" | "COLLECTED">("ALL");
  const [error, setError] = useState("");

  useEffect(() => {
    checkAdminAccess();
    loadOrders();
  }, []);

  const checkAdminAccess = () => {
    if (!state.isAuthenticated || state.user?.role !== "ADMIN") {
      Alert.alert(
        "Access Denied",
        "You do not have admin privileges to access this area.",
        [{ text: "OK", onPress: () => router.back() }]
      );
      return;
    }
  };

  const loadOrders = async () => {
    try {
      setLoading(true);
      setError("");
      // Note: In a real implementation, you would have an admin orders endpoint
      // For now, we'll simulate the data
      const ordersData: Order[] = [];
      setOrders(ordersData);
    } catch (error) {
      console.error("Failed to load orders:", error);
      setError("Failed to load orders");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  const handleStatusChange = async (
    orderId: number,
    newStatus: "PENDING" | "COLLECTED"
  ) => {
    try {
      // Note: Implement status update endpoint in backend
      console.log("Update order status:", orderId, newStatus);

      // Update local state
      setOrders((prev) =>
        prev.map((order) =>
          order.id === orderId ? { ...order, status: newStatus } : order
        )
      );

      Alert.alert("Success", `Order status updated to ${newStatus}`);
    } catch (error) {
      console.error("Failed to update order status:", error);
      Alert.alert("Error", "Failed to update order status");
    }
  };

  const handleDeleteOrder = (order: Order) => {
    Alert.alert(
      "Delete Order",
      `Are you sure you want to delete order #${order.id}? This action cannot be undone.`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => confirmDeleteOrder(order.id),
        },
      ]
    );
  };

  const confirmDeleteOrder = async (orderId: number) => {
    try {
      // Note: Implement delete endpoint in backend
      console.log("Delete order:", orderId);

      // Update local state
      setOrders((prev) => prev.filter((order) => order.id !== orderId));

      Alert.alert("Success", "Order deleted successfully");
    } catch (error) {
      console.error("Failed to delete order:", error);
      Alert.alert("Error", "Failed to delete order");
    }
  };

  const filteredOrders = orders.filter((order) => {
    if (filter === "ALL") return true;
    return order.status === filter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return colors.warning[500];
      case "COLLECTED":
        return colors.success[500];
      default:
        return colors.text.secondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "PENDING":
        return "⏳";
      case "COLLECTED":
        return "✅";
      default:
        return "📋";
    }
  };

  if (loading && orders.length === 0) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
            Loading orders...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Text style={[styles.backButtonText, { color: colors.text.primary }]}>
            ← Back
          </Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          Orders
        </Text>
        <View style={styles.placeholder} />
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.filterTabs}>
            {(["ALL", "PENDING", "COLLECTED"] as const).map((filterOption) => (
              <TouchableOpacity
                key={filterOption}
                style={[
                  styles.filterTab,
                  {
                    backgroundColor:
                      filter === filterOption
                        ? colors.primary[500]
                        : colors.background.secondary,
                  },
                ]}
                onPress={() => setFilter(filterOption)}
              >
                <Text
                  style={[
                    styles.filterTabText,
                    {
                      color:
                        filter === filterOption
                          ? colors.white
                          : colors.text.primary,
                    },
                  ]}
                >
                  {filterOption}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {error ? (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.error }]}>
            {error}
          </Text>
          <Button
            title="Retry"
            onPress={loadOrders}
            variant="outline"
            size="sm"
          />
        </View>
      ) : null}

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Orders Count */}
        <Card variant="default" padding="md" style={styles.statsCard}>
          <Text style={[styles.statsText, { color: colors.text.primary }]}>
            📋 {filteredOrders.length} Orders{" "}
            {filter !== "ALL" ? `(${filter})` : ""}
          </Text>
        </Card>

        {/* Orders List */}
        {filteredOrders.length === 0 ? (
          <Card variant="elevated" padding="xl" style={styles.emptyCard}>
            <Text style={[styles.emptyTitle, { color: colors.text.primary }]}>
              {filter === "ALL" ? "No Orders Yet" : `No ${filter} Orders`}
            </Text>
            <Text
              style={[styles.emptySubtitle, { color: colors.text.secondary }]}
            >
              {filter === "ALL"
                ? "Orders will appear here when customers place them"
                : `No orders with ${filter.toLowerCase()} status found`}
            </Text>
          </Card>
        ) : (
          <View style={styles.ordersList}>
            {filteredOrders.map((order) => (
              <Card
                key={order.id}
                variant="elevated"
                padding="lg"
                style={styles.orderCard}
              >
                <View style={styles.orderHeader}>
                  <View style={styles.orderInfo}>
                    <Text
                      style={[
                        styles.orderNumber,
                        { color: colors.text.primary },
                      ]}
                    >
                      Order #{order.id}
                    </Text>
                    <Text
                      style={[
                        styles.customerName,
                        { color: colors.text.secondary },
                      ]}
                    >
                      {order.customerName}
                    </Text>
                    <Text
                      style={[
                        styles.orderDate,
                        { color: colors.text.tertiary },
                      ]}
                    >
                      {new Date(order.createdAt).toLocaleDateString()}
                    </Text>
                  </View>
                  <View style={styles.orderStatus}>
                    <View
                      style={[
                        styles.statusBadge,
                        {
                          backgroundColor: getStatusColor(order.status) + "20",
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.statusText,
                          { color: getStatusColor(order.status) },
                        ]}
                      >
                        {getStatusIcon(order.status)} {order.status}
                      </Text>
                    </View>
                  </View>
                </View>

                <View style={styles.orderDetails}>
                  <Text
                    style={[styles.productName, { color: colors.text.primary }]}
                  >
                    {order.product.name}
                  </Text>
                  {order.isCustomized && (
                    <Text
                      style={[
                        styles.customizedLabel,
                        { color: colors.primary[500] },
                      ]}
                    >
                      🎨 Customized Product
                    </Text>
                  )}
                  <Text
                    style={[
                      styles.orderQuantity,
                      { color: colors.text.secondary },
                    ]}
                  >
                    Quantity: {order.quantity}
                  </Text>
                  {order.totalPrice && (
                    <Text
                      style={[
                        styles.orderTotal,
                        { color: colors.primary[500] },
                      ]}
                    >
                      Total: {formatPrice(order.totalPrice)}
                    </Text>
                  )}
                </View>

                <View style={styles.orderActions}>
                  {order.status === "PENDING" ? (
                    <TouchableOpacity
                      style={[
                        styles.actionButton,
                        { backgroundColor: colors.success[500] },
                      ]}
                      onPress={() => handleStatusChange(order.id, "COLLECTED")}
                    >
                      <Text style={styles.actionButtonText}>
                        ✅ Mark Collected
                      </Text>
                    </TouchableOpacity>
                  ) : (
                    <TouchableOpacity
                      style={[
                        styles.actionButton,
                        { backgroundColor: colors.warning[500] },
                      ]}
                      onPress={() => handleStatusChange(order.id, "PENDING")}
                    >
                      <Text style={styles.actionButtonText}>
                        ⏳ Mark Pending
                      </Text>
                    </TouchableOpacity>
                  )}
                  <TouchableOpacity
                    style={[
                      styles.actionButton,
                      { backgroundColor: colors.error },
                    ]}
                    onPress={() => handleDeleteOrder(order)}
                  >
                    <Text style={styles.actionButtonText}>🗑️ Delete</Text>
                  </TouchableOpacity>
                </View>
              </Card>
            ))}
          </View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  backButton: {
    padding: Spacing.sm,
  },
  backButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  placeholder: {
    width: 60,
  },
  filterContainer: {
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  filterTabs: {
    flexDirection: "row",
    paddingHorizontal: Spacing.lg,
    gap: Spacing.sm,
  },
  filterTab: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
  },
  filterTabText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  statsCard: {
    marginTop: Spacing.lg,
    marginBottom: Spacing.md,
  },
  statsText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    textAlign: "center",
  },
  errorContainer: {
    padding: Spacing.lg,
    alignItems: "center",
  },
  errorText: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.md,
  },
  emptyCard: {
    alignItems: "center",
    marginTop: Spacing.xl,
  },
  emptyTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    marginBottom: Spacing.sm,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    lineHeight: 24,
  },
  ordersList: {
    gap: Spacing.md,
  },
  orderCard: {
    marginBottom: Spacing.md,
  },
  orderHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: Spacing.md,
  },
  orderInfo: {
    flex: 1,
  },
  orderNumber: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  customerName: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
    marginBottom: Spacing.xs,
  },
  orderDate: {
    fontSize: Typography.fontSize.sm,
  },
  orderStatus: {
    alignItems: "flex-end",
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  statusText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: "600",
  },
  orderDetails: {
    marginBottom: Spacing.md,
  },
  productName: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  customizedLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "500",
    marginBottom: Spacing.xs,
  },
  orderQuantity: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Spacing.xs,
  },
  orderTotal: {
    fontSize: Typography.fontSize.base,
    fontWeight: "bold",
  },
  orderActions: {
    flexDirection: "row",
    gap: Spacing.sm,
  },
  actionButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    alignItems: "center",
  },
  actionButtonText: {
    color: Colors.white,
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "500",
  },
});
