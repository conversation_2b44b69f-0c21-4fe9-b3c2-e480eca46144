/**
 * Six Light Media Store - Mobile Admin Products Management
 * Complete product management with ImageKit integration
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { apiClient, Product } from "@/services/api";
import ProductForm from "@/components/admin/ProductForm";

export default function AdminProductsScreen() {
  const { colors } = useTheme();
  const { state } = useApp();
  const router = useRouter();

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [error, setError] = useState("");

  useEffect(() => {
    checkAdminAccess();
    loadProducts();
  }, []);

  const checkAdminAccess = () => {
    if (!state.isAuthenticated || state.user?.role !== "ADMIN") {
      Alert.alert(
        "Access Denied",
        "You do not have admin privileges to access this area.",
        [{ text: "OK", onPress: () => router.back() }]
      );
      return;
    }
  };

  const loadProducts = async () => {
    try {
      setLoading(true);
      setError("");
      const productsData = await apiClient.getProducts();
      setProducts(productsData);
    } catch (error) {
      console.error("Failed to load products:", error);
      setError("Failed to load products");
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadProducts();
    setRefreshing(false);
  };

  const handleAddProduct = () => {
    setEditingProduct(null);
    setShowAddModal(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowAddModal(true);
  };

  const handleDeleteProduct = (product: Product) => {
    Alert.alert(
      "Delete Product",
      `Are you sure you want to delete "${product.name}"? This action cannot be undone.`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => confirmDeleteProduct(product.id),
        },
      ]
    );
  };

  const confirmDeleteProduct = async (productId: number) => {
    try {
      // Note: Implement delete endpoint in backend
      console.log("Delete product:", productId);
      Alert.alert("Success", "Product deleted successfully");
      await loadProducts(); // Refresh the list
    } catch (error) {
      console.error("Failed to delete product:", error);
      Alert.alert("Error", "Failed to delete product");
    }
  };

  const handleProductSaved = () => {
    setShowAddModal(false);
    setEditingProduct(null);
    loadProducts(); // Refresh the list
  };

  const handleCloseModal = () => {
    setShowAddModal(false);
    setEditingProduct(null);
  };

  if (loading && products.length === 0) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
            Loading products...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Text style={[styles.backButtonText, { color: colors.text.primary }]}>
            ← Back
          </Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          Products
        </Text>
        <TouchableOpacity onPress={handleAddProduct} style={styles.addButton}>
          <Text style={[styles.addButtonText, { color: colors.primary[500] }]}>
            + Add
          </Text>
        </TouchableOpacity>
      </View>

      {error ? (
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.error }]}>
            {error}
          </Text>
          <Button
            title="Retry"
            onPress={loadProducts}
            variant="outline"
            size="sm"
          />
        </View>
      ) : null}

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Products Count */}
        <Card variant="default" padding="md" style={styles.statsCard}>
          <Text style={[styles.statsText, { color: colors.text.primary }]}>
            📦 {products.length} Products
          </Text>
        </Card>

        {/* Products List */}
        {products.length === 0 ? (
          <Card variant="elevated" padding="xl" style={styles.emptyCard}>
            <Text style={[styles.emptyTitle, { color: colors.text.primary }]}>
              No Products Yet
            </Text>
            <Text
              style={[styles.emptySubtitle, { color: colors.text.secondary }]}
            >
              Start by adding your first product to the store
            </Text>
            <Button
              title="Add First Product"
              onPress={handleAddProduct}
              variant="primary"
              style={styles.addFirstButton}
            />
          </Card>
        ) : (
          <View style={styles.productsList}>
            {products.map((product) => (
              <Card
                key={product.id}
                variant="elevated"
                padding="lg"
                style={styles.productCard}
              >
                <View style={styles.productHeader}>
                  <View style={styles.productInfo}>
                    <Text
                      style={[
                        styles.productName,
                        { color: colors.text.primary },
                      ]}
                    >
                      {product.name}
                    </Text>
                    <Text
                      style={[
                        styles.productPrice,
                        { color: colors.primary[500] },
                      ]}
                    >
                      ${product.price}
                    </Text>
                    {product.customizable && (
                      <View
                        style={[
                          styles.customizableBadge,
                          { backgroundColor: colors.secondary[100] },
                        ]}
                      >
                        <Text
                          style={[
                            styles.customizableText,
                            { color: colors.secondary[700] },
                          ]}
                        >
                          🎨 Customizable
                        </Text>
                      </View>
                    )}
                  </View>
                  <View
                    style={[
                      styles.productImage,
                      { backgroundColor: colors.background.secondary },
                    ]}
                  >
                    <Text
                      style={[
                        styles.imagePlaceholder,
                        { color: colors.text.tertiary },
                      ]}
                    >
                      📷
                    </Text>
                  </View>
                </View>

                <Text
                  style={[
                    styles.productDescription,
                    { color: colors.text.secondary },
                  ]}
                  numberOfLines={2}
                >
                  {product.description}
                </Text>

                <View style={styles.productActions}>
                  <TouchableOpacity
                    style={[
                      styles.actionButton,
                      { backgroundColor: colors.primary[500] },
                    ]}
                    onPress={() => handleEditProduct(product)}
                  >
                    <Text style={styles.actionButtonText}>✏️ Edit</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.actionButton,
                      { backgroundColor: colors.error },
                    ]}
                    onPress={() => handleDeleteProduct(product)}
                  >
                    <Text style={styles.actionButtonText}>🗑️ Delete</Text>
                  </TouchableOpacity>
                </View>
              </Card>
            ))}
          </View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Add/Edit Product Modal */}
      <Modal
        visible={showAddModal}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        <ProductForm
          product={editingProduct}
          onSave={handleProductSaved}
          onCancel={handleCloseModal}
        />
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  backButton: {
    padding: Spacing.sm,
  },
  backButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  addButton: {
    padding: Spacing.sm,
  },
  addButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  statsCard: {
    marginTop: Spacing.lg,
    marginBottom: Spacing.md,
  },
  statsText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    textAlign: "center",
  },
  errorContainer: {
    padding: Spacing.lg,
    alignItems: "center",
  },
  errorText: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.md,
  },
  emptyCard: {
    alignItems: "center",
    marginTop: Spacing.xl,
  },
  emptyTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    marginBottom: Spacing.sm,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.lg,
    lineHeight: 24,
  },
  addFirstButton: {
    paddingHorizontal: Spacing.xl,
  },
  productsList: {
    gap: Spacing.md,
  },
  productCard: {
    marginBottom: Spacing.md,
  },
  productHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: Spacing.md,
  },
  productInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },
  productName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  productPrice: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.sm,
  },
  customizableBadge: {
    alignSelf: "flex-start",
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  customizableText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: "600",
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.md,
    justifyContent: "center",
    alignItems: "center",
  },
  imagePlaceholder: {
    fontSize: 24,
  },
  productDescription: {
    fontSize: Typography.fontSize.sm,
    lineHeight: 20,
    marginBottom: Spacing.md,
  },
  productActions: {
    flexDirection: "row",
    gap: Spacing.sm,
  },
  actionButton: {
    flex: 1,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.md,
    alignItems: "center",
  },
  actionButtonText: {
    color: Colors.white,
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "500",
  },
});
