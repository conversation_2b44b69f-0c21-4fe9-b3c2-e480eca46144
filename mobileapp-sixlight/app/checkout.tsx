/**
 * Six Light Media Store - Checkout Screen
 * Order creation and customer information collection
 */

import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { apiClient, formatPrice } from "@/services/api";

interface CustomerInfo {
  customerName: string;
  customerPhone: string;
  customerAddress: string;
}

export default function CheckoutScreen() {
  const { colors } = useTheme();
  const { state, clearCart } = useApp();
  const router = useRouter();

  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    customerName: "",
    customerPhone: "",
    customerAddress: "",
  });
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: keyof CustomerInfo, value: string) => {
    setCustomerInfo((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = (): boolean => {
    if (!customerInfo.customerName.trim()) {
      Alert.alert("Error", "Please enter your name");
      return false;
    }
    if (!customerInfo.customerPhone.trim()) {
      Alert.alert("Error", "Please enter your phone number");
      return false;
    }
    if (!customerInfo.customerAddress.trim()) {
      Alert.alert("Error", "Please enter your address");
      return false;
    }
    return true;
  };

  const handlePlaceOrder = async () => {
    if (!validateForm()) return;

    if (state.cart.length === 0) {
      Alert.alert("Error", "Your cart is empty");
      return;
    }

    if (!state.isAuthenticated) {
      Alert.alert("Login Required", "Please login to place an order", [
        { text: "Cancel", style: "cancel" },
        { text: "Login", onPress: () => router.push("/auth/login") },
      ]);
      return;
    }

    try {
      setLoading(true);

      // Create orders for each cart item (backend expects individual orders)
      const orderPromises = state.cart.map((item) => {
        const customization = item.customization;

        return apiClient.createOrder({
          productId: item.product.id,
          customerName: customerInfo.customerName,
          customerPhone: customerInfo.customerPhone,
          customerAddress: customerInfo.customerAddress,
          customColor: customization?.customColor,
          customText: customization?.customText,
          isCustomized: customization?.isCustomized || false,
          customizationData: customization?.customizationData,
          customizationPreview: customization?.customizationPreview,
          quantity: item.quantity,
        });
      });

      await Promise.all(orderPromises);

      // Clear cart after successful order
      clearCart();

      Alert.alert(
        "Order Placed Successfully!",
        "Your order has been placed and will be processed soon. You can track your orders in your profile.",
        [
          {
            text: "View Orders",
            onPress: () => {
              router.replace("/profile");
            },
          },
          {
            text: "Continue Shopping",
            onPress: () => {
              router.replace("/");
            },
          },
        ]
      );
    } catch (error) {
      console.error("Order creation failed:", error);
      Alert.alert(
        "Order Failed",
        "Failed to place your order. Please try again.",
        [{ text: "OK" }]
      );
    } finally {
      setLoading(false);
    }
  };

  if (state.cart.length === 0) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Text
              style={[styles.backButtonText, { color: colors.text.primary }]}
            >
              ← Back
            </Text>
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
            Checkout
          </Text>
          <View style={styles.placeholder} />
        </View>

        <View style={styles.emptyContainer}>
          <Text style={[styles.emptyTitle, { color: colors.text.primary }]}>
            Your cart is empty
          </Text>
          <Text
            style={[styles.emptySubtitle, { color: colors.text.secondary }]}
          >
            Add some products to proceed with checkout
          </Text>
          <Button
            title="Start Shopping"
            onPress={() => router.replace("/explore")}
            variant="primary"
            style={styles.startShoppingButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Text style={[styles.backButtonText, { color: colors.text.primary }]}>
            ← Back
          </Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          Checkout
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Order Summary */}
        <Card variant="elevated" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Order Summary
          </Text>
          {state.cart.map((item, index) => (
            <View key={item.id} style={styles.orderItem}>
              <View style={styles.itemInfo}>
                <Text style={[styles.itemName, { color: colors.text.primary }]}>
                  {item.product.name}
                </Text>
                {item.customization?.isCustomized && (
                  <Text
                    style={[
                      styles.customizedLabel,
                      { color: colors.primary[500] },
                    ]}
                  >
                    ✓ Customized
                  </Text>
                )}
                <Text
                  style={[styles.itemDetails, { color: colors.text.secondary }]}
                >
                  Qty: {item.quantity} × {formatPrice(item.price)}
                </Text>
              </View>
              <Text style={[styles.itemTotal, { color: colors.text.primary }]}>
                {formatPrice(item.price * item.quantity)}
              </Text>
            </View>
          ))}

          <View
            style={[styles.divider, { backgroundColor: colors.border.light }]}
          />

          <View style={styles.totalRow}>
            <Text style={[styles.totalLabel, { color: colors.text.primary }]}>
              Total
            </Text>
            <Text style={[styles.totalAmount, { color: colors.primary[500] }]}>
              {formatPrice(state.cartTotal)}
            </Text>
          </View>
        </Card>

        {/* Customer Information */}
        <Card variant="elevated" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Customer Information
          </Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
              Full Name *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: colors.background.secondary,
                  color: colors.text.primary,
                  borderColor: colors.border.light,
                },
              ]}
              placeholder="Enter your full name"
              placeholderTextColor={colors.text.tertiary}
              value={customerInfo.customerName}
              onChangeText={(value) => handleInputChange("customerName", value)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
              Phone Number *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: colors.background.secondary,
                  color: colors.text.primary,
                  borderColor: colors.border.light,
                },
              ]}
              placeholder="Enter your phone number"
              placeholderTextColor={colors.text.tertiary}
              value={customerInfo.customerPhone}
              onChangeText={(value) =>
                handleInputChange("customerPhone", value)
              }
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
              Delivery Address *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                styles.textArea,
                {
                  backgroundColor: colors.background.secondary,
                  color: colors.text.primary,
                  borderColor: colors.border.light,
                },
              ]}
              placeholder="Enter your complete address"
              placeholderTextColor={colors.text.tertiary}
              value={customerInfo.customerAddress}
              onChangeText={(value) =>
                handleInputChange("customerAddress", value)
              }
              multiline
              numberOfLines={3}
            />
          </View>
        </Card>

        {/* Payment Info */}
        <Card variant="elevated" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Payment Method
          </Text>
          <View
            style={[
              styles.paymentInfo,
              { backgroundColor: colors.background.secondary },
            ]}
          >
            <Text style={[styles.paymentText, { color: colors.text.primary }]}>
              💰 Cash on Delivery
            </Text>
            <Text
              style={[styles.paymentSubtext, { color: colors.text.secondary }]}
            >
              Pay when your order is delivered
            </Text>
          </View>
        </Card>
      </ScrollView>

      {/* Place Order Button */}
      <View
        style={[styles.footer, { backgroundColor: colors.background.primary }]}
      >
        <Button
          title={loading ? "Placing Order..." : "Place Order"}
          onPress={handlePlaceOrder}
          variant="primary"
          size="lg"
          fullWidth
          loading={loading}
          disabled={loading}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  backButton: {
    padding: Spacing.sm,
  },
  backButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  placeholder: {
    width: 60,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  section: {
    marginTop: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.md,
  },
  orderItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: Spacing.md,
  },
  itemInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },
  itemName: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  customizedLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "500",
    marginBottom: Spacing.xs,
  },
  itemDetails: {
    fontSize: Typography.fontSize.sm,
  },
  itemTotal: {
    fontSize: Typography.fontSize.base,
    fontWeight: "bold",
  },
  divider: {
    height: 1,
    marginVertical: Spacing.md,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  totalLabel: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
  },
  totalAmount: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  inputLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.sm,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    fontSize: Typography.fontSize.base,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  paymentInfo: {
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    alignItems: "center",
  },
  paymentText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  paymentSubtext: {
    fontSize: Typography.fontSize.sm,
    textAlign: "center",
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
  },
  emptyTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    marginBottom: Spacing.sm,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.xl,
    lineHeight: 24,
  },
  startShoppingButton: {
    paddingHorizontal: Spacing.xl,
  },
});
