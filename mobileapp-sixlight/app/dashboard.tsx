/**
 * Six Light Media Store - User Dashboard
 * Secure user dashboard with profile, orders, and account management
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Alert,
  Image,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { apiClient, formatPrice, formatDate, Order } from "@/services/api";
import RouteGuard from "@/components/auth/RouteGuard";

interface DashboardData {
  user: any;
  orders: Order[];
}

export default function UserDashboardScreen() {
  const { colors } = useTheme();
  const { state, logout } = useApp();
  const router = useRouter();

  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError("");

      const dashboardData = await apiClient.getUserDashboard();
      setData(dashboardData);
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load dashboard data"
      );
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleLogout = () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Logout",
        style: "destructive",
        onPress: async () => {
          await logout();
          router.replace("/");
        },
      },
    ]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return colors.warning;
      case "COLLECTED":
        return colors.success;
      default:
        return colors.secondary[500];
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "PENDING":
        return "Pending";
      case "COLLECTED":
        return "Ready for Collection";
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
            Loading your dashboard...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.error }]}>
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={loadDashboardData}
            variant="primary"
            style={styles.retryButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <RouteGuard requireAuth={true} fallbackRoute="/">
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={colors.primary[500]}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <View>
              <Text style={[styles.greeting, { color: colors.text.secondary }]}>
                Welcome back,
              </Text>
              <Text style={[styles.userName, { color: colors.text.primary }]}>
                {data?.user?.name || state.user?.name || "User"}
              </Text>
            </View>
            <TouchableOpacity
              style={[
                styles.logoutButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={handleLogout}
            >
              <Text style={[styles.logoutText, { color: colors.error }]}>
                Logout
              </Text>
            </TouchableOpacity>
          </View>

          {/* Profile Card */}
          <Card variant="elevated" padding="lg" style={styles.profileCard}>
            <View style={styles.profileHeader}>
              <View style={styles.profileInfo}>
                <View
                  style={[
                    styles.avatar,
                    { backgroundColor: colors.primary[100] },
                  ]}
                >
                  {data?.user?.profileImage ? (
                    <Image
                      source={{ uri: data.user.profileImage }}
                      style={styles.avatarImage}
                      resizeMode="cover"
                    />
                  ) : (
                    <Text
                      style={[
                        styles.avatarText,
                        { color: colors.primary[700] },
                      ]}
                    >
                      👤
                    </Text>
                  )}
                </View>
                <View style={styles.userDetails}>
                  <Text
                    style={[styles.profileName, { color: colors.text.primary }]}
                  >
                    {data?.user?.name || state.user?.name}
                  </Text>
                  <Text
                    style={[
                      styles.profileEmail,
                      { color: colors.text.secondary },
                    ]}
                  >
                    {data?.user?.email || state.user?.email}
                  </Text>
                  <View
                    style={[
                      styles.roleBadge,
                      { backgroundColor: colors.secondary[200] },
                    ]}
                  >
                    <Text
                      style={[styles.roleText, { color: colors.text.primary }]}
                    >
                      {data?.user?.role || state.user?.role || "USER"}
                    </Text>
                  </View>
                </View>
              </View>
              <TouchableOpacity
                style={[
                  styles.editButton,
                  { borderColor: colors.primary[500] },
                ]}
                onPress={() => router.push("/profile/edit")}
              >
                <Text
                  style={[
                    styles.editButtonText,
                    { color: colors.primary[500] },
                  ]}
                >
                  Edit
                </Text>
              </TouchableOpacity>
            </View>
          </Card>

          {/* Quick Stats */}
          <View style={styles.statsContainer}>
            <Card variant="elevated" padding="md" style={styles.statCard}>
              <Text style={[styles.statNumber, { color: colors.primary[500] }]}>
                {data?.orders?.length || 0}
              </Text>
              <Text
                style={[styles.statLabel, { color: colors.text.secondary }]}
              >
                Total Orders
              </Text>
            </Card>
            <Card variant="elevated" padding="md" style={styles.statCard}>
              <Text style={[styles.statNumber, { color: colors.primary[500] }]}>
                {data?.orders?.filter((order) => order.status === "PENDING")
                  .length || 0}
              </Text>
              <Text
                style={[styles.statLabel, { color: colors.text.secondary }]}
              >
                Pending
              </Text>
            </Card>
            <Card variant="elevated" padding="md" style={styles.statCard}>
              <Text style={[styles.statNumber, { color: colors.primary[500] }]}>
                {data?.orders?.filter((order) => order.status === "COLLECTED")
                  .length || 0}
              </Text>
              <Text
                style={[styles.statLabel, { color: colors.text.secondary }]}
              >
                Completed
              </Text>
            </Card>
          </View>

          {/* Quick Actions */}
          <Card variant="elevated" padding="lg" style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
              Quick Actions
            </Text>
            <View style={styles.actionsGrid}>
              <TouchableOpacity
                style={[
                  styles.actionCard,
                  { backgroundColor: colors.primary[500] },
                ]}
                onPress={() => router.push("/explore")}
              >
                <Text style={styles.actionIcon}>🛍️</Text>
                <Text style={styles.actionTitle}>Shop Products</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.actionCard,
                  { backgroundColor: colors.secondary[500] },
                ]}
                onPress={() => router.push("/orders")}
              >
                <Text style={styles.actionIcon}>📦</Text>
                <Text style={styles.actionTitle}>My Orders</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionCard, { backgroundColor: colors.success }]}
                onPress={() => router.push("/(tabs)/wishlist")}
              >
                <Text style={styles.actionIcon}>❤️</Text>
                <Text style={styles.actionTitle}>Wishlist</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionCard, { backgroundColor: colors.info }]}
                onPress={() => router.push("/profile/edit")}
              >
                <Text style={styles.actionIcon}>⚙️</Text>
                <Text style={styles.actionTitle}>Settings</Text>
              </TouchableOpacity>
            </View>
          </Card>

          {/* Recent Orders */}
          <Card variant="elevated" padding="lg" style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text
                style={[styles.sectionTitle, { color: colors.text.primary }]}
              >
                Recent Orders
              </Text>
              <TouchableOpacity onPress={() => router.push("/orders")}>
                <Text
                  style={[styles.seeAllText, { color: colors.primary[500] }]}
                >
                  See All
                </Text>
              </TouchableOpacity>
            </View>

            {data?.orders && data.orders.length > 0 ? (
              data.orders.slice(0, 3).map((order) => (
                <View key={order.id} style={styles.orderItem}>
                  <View style={styles.orderInfo}>
                    <Text
                      style={[
                        styles.orderNumber,
                        { color: colors.text.primary },
                      ]}
                    >
                      Order #{order.id}
                    </Text>
                    <Text
                      style={[
                        styles.orderDate,
                        { color: colors.text.secondary },
                      ]}
                    >
                      {formatDate(order.createdAt)}
                    </Text>
                  </View>
                  <View style={styles.orderRight}>
                    <Text
                      style={[
                        styles.orderTotal,
                        { color: colors.text.primary },
                      ]}
                    >
                      {formatPrice(order.totalPrice || 0)}
                    </Text>
                    <View
                      style={[
                        styles.statusBadge,
                        {
                          backgroundColor: getStatusColor(order.status) + "20",
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.statusText,
                          { color: getStatusColor(order.status) },
                        ]}
                      >
                        {getStatusText(order.status)}
                      </Text>
                    </View>
                  </View>
                </View>
              ))
            ) : (
              <View style={styles.emptyOrders}>
                <Text
                  style={[styles.emptyText, { color: colors.text.secondary }]}
                >
                  No orders yet. Start shopping to see your orders here!
                </Text>
                <Button
                  title="Start Shopping"
                  onPress={() => router.push("/explore")}
                  variant="primary"
                  size="sm"
                  style={styles.shopButton}
                />
              </View>
            )}
          </Card>

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </SafeAreaView>
    </RouteGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "500",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
  },
  errorText: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.lg,
  },
  retryButton: {
    marginTop: Spacing.md,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
  },
  greeting: {
    fontSize: Typography.fontSize.base,
    fontWeight: "400",
  },
  userName: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
  },
  logoutButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  logoutText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  profileCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  profileHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  profileInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.md,
  },
  avatarImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  avatarText: {
    fontSize: 30,
  },
  userDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  profileEmail: {
    fontSize: Typography.fontSize.base,
    marginBottom: Spacing.sm,
  },
  roleBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
    alignSelf: "flex-start",
  },
  roleText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: "600",
  },
  editButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderWidth: 1,
    borderRadius: BorderRadius.md,
  },
  editButtonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  statsContainer: {
    flexDirection: "row",
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    gap: Spacing.md,
  },
  statCard: {
    flex: 1,
    alignItems: "center",
    paddingVertical: Spacing.lg,
  },
  statNumber: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  statLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "500",
  },
  section: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  seeAllText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  actionsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.md,
  },
  actionCard: {
    flex: 1,
    minWidth: "45%",
    padding: Spacing.lg,
    borderRadius: BorderRadius.lg,
    alignItems: "center",
  },
  actionIcon: {
    fontSize: 24,
    marginBottom: Spacing.sm,
  },
  actionTitle: {
    color: Colors.white,
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
    textAlign: "center",
  },
  orderItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.secondary[200],
  },
  orderInfo: {
    flex: 1,
  },
  orderNumber: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  orderDate: {
    fontSize: Typography.fontSize.sm,
  },
  orderRight: {
    alignItems: "flex-end",
  },
  orderTotal: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
  },
  statusText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: "600",
  },
  emptyOrders: {
    alignItems: "center",
    paddingVertical: Spacing.xl,
  },
  emptyText: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.lg,
  },
  shopButton: {
    marginTop: Spacing.md,
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
});
