/**
 * Six Light Media Store - Orders Screen
 * User order history and tracking
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Image,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { formatPrice, formatDate, apiClient, Order } from "@/services/api";

// Using Order interface from API

export default function OrdersScreen() {
  const { colors } = useTheme();
  const { state } = useApp();
  const router = useRouter();

  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async () => {
    try {
      setLoading(true);
      setError("");

      // Check if user is authenticated
      if (!state.user) {
        setError("Please log in to view your orders");
        return;
      }

      const fetchedOrders = await apiClient.getOrders();
      setOrders(fetchedOrders);
    } catch (error) {
      console.error("Failed to load orders:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load orders"
      );
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  const getStatusColor = (status: Order["status"]) => {
    switch (status) {
      case "PENDING":
        return colors.warning;
      case "COLLECTED":
        return colors.success;
      default:
        return colors.secondary[500];
    }
  };

  const getStatusText = (status: Order["status"]) => {
    switch (status) {
      case "PENDING":
        return "Pending";
      case "COLLECTED":
        return "Ready for Collection";
      default:
        return status;
    }
  };

  const renderOrderItem = ({ item }: { item: Order }) => (
    <Card variant="elevated" padding="lg" style={styles.orderCard}>
      <View style={styles.orderHeader}>
        <View>
          <Text style={[styles.orderNumber, { color: colors.text.primary }]}>
            Order #{item.id}
          </Text>
          <Text style={[styles.orderDate, { color: colors.text.secondary }]}>
            {formatDate(item.createdAt)}
          </Text>
        </View>
        <View
          style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(item.status) + "20" },
          ]}
        >
          <Text
            style={[styles.statusText, { color: getStatusColor(item.status) }]}
          >
            {getStatusText(item.status)}
          </Text>
        </View>
      </View>

      <View style={styles.orderItems}>
        <View style={styles.orderItem}>
          <View
            style={[
              styles.itemImage,
              { backgroundColor: colors.background.secondary },
            ]}
          >
            {item.product.image ? (
              <Image
                source={{ uri: item.product.image }}
                style={styles.productImage}
                resizeMode="cover"
              />
            ) : (
              <View
                style={[
                  styles.imagePlaceholder,
                  { backgroundColor: colors.secondary[300] },
                ]}
              />
            )}
          </View>
          <View style={styles.itemDetails}>
            <Text style={[styles.itemName, { color: colors.text.primary }]}>
              {item.product.name}
            </Text>
            <Text
              style={[styles.itemQuantity, { color: colors.text.secondary }]}
            >
              Qty: {item.quantity} × {formatPrice(item.unitPrice || 0)}
            </Text>
            {item.isCustomized && (
              <View
                style={[
                  styles.customBadge,
                  { backgroundColor: colors.primary[100] },
                ]}
              >
                <Text
                  style={[
                    styles.customBadgeText,
                    { color: colors.primary[700] },
                  ]}
                >
                  Customized
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>

      <View style={styles.orderFooter}>
        <Text style={[styles.orderTotal, { color: colors.text.primary }]}>
          Total:{" "}
          <Text style={{ color: colors.primary[500], fontWeight: "bold" }}>
            {formatPrice(item.totalPrice || 0)}
          </Text>
        </Text>
        <View style={styles.orderActions}>
          <Button
            title="View Details"
            onPress={() => {
              /* TODO: Navigate to order details */
            }}
            variant="outline"
            size="sm"
            style={styles.actionButton}
          />
          {item.status === "COLLECTED" && (
            <Button
              title="Reorder"
              onPress={() => {
                router.push(`/product/${item.product.id}`);
              }}
              variant="ghost"
              size="sm"
              style={styles.actionButton}
            />
          )}
        </View>
      </View>
    </Card>
  );

  if (loading) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Text style={[styles.backButton, { color: colors.primary[500] }]}>
              ← Back
            </Text>
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
            My Orders
          </Text>
          <View style={styles.headerSpacer} />
        </View>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
            Loading your orders...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Text style={[styles.backButton, { color: colors.primary[500] }]}>
            ← Back
          </Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          My Orders
        </Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Orders List */}
      <FlatList
        data={orders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.ordersList}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary[500]}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            {error ? (
              <>
                <View
                  style={[
                    styles.emptyIcon,
                    { backgroundColor: colors.error + "20" },
                  ]}
                >
                  <Text style={[styles.errorIcon, { color: colors.error }]}>
                    ⚠️
                  </Text>
                </View>
                <Text
                  style={[styles.emptyTitle, { color: colors.text.primary }]}
                >
                  Failed to Load Orders
                </Text>
                <Text
                  style={[
                    styles.emptySubtitle,
                    { color: colors.text.secondary },
                  ]}
                >
                  {error}
                </Text>
                <Button
                  title="Try Again"
                  onPress={loadOrders}
                  variant="primary"
                  style={styles.startShoppingButton}
                />
              </>
            ) : (
              <>
                <View
                  style={[
                    styles.emptyIcon,
                    { backgroundColor: colors.secondary[200] },
                  ]}
                >
                  <Text style={styles.emptyIconText}>📦</Text>
                </View>
                <Text
                  style={[styles.emptyTitle, { color: colors.text.primary }]}
                >
                  No orders yet
                </Text>
                <Text
                  style={[
                    styles.emptySubtitle,
                    { color: colors.text.secondary },
                  ]}
                >
                  Start shopping to see your orders here
                </Text>
                <Button
                  title="Start Shopping"
                  onPress={() => router.push("/explore")}
                  variant="primary"
                  style={styles.startShoppingButton}
                />
              </>
            )}
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  backButton: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  headerSpacer: {
    width: 50,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: Typography.fontSize.base,
  },
  ordersList: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing["3xl"],
  },
  orderCard: {
    marginBottom: Spacing.md,
  },
  orderHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: Spacing.md,
  },
  orderNumber: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  orderDate: {
    fontSize: Typography.fontSize.sm,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  statusText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  orderItems: {
    marginBottom: Spacing.md,
  },
  orderItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Spacing.sm,
  },
  itemImage: {
    width: 50,
    height: 50,
    borderRadius: BorderRadius.md,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Spacing.md,
  },
  imagePlaceholder: {
    width: 30,
    height: 30,
    borderRadius: BorderRadius.sm,
  },
  productImage: {
    width: 50,
    height: 50,
    borderRadius: BorderRadius.md,
  },
  itemDetails: {
    flex: 1,
  },
  customBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
    alignSelf: "flex-start",
    marginTop: Spacing.xs,
  },
  customBadgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: "600",
  },
  itemName: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  itemQuantity: {
    fontSize: Typography.fontSize.sm,
  },
  orderFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  orderTotal: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  orderActions: {
    flexDirection: "row",
    gap: Spacing.sm,
  },
  actionButton: {
    paddingHorizontal: Spacing.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: Spacing["3xl"],
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: Spacing.lg,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyIconText: {
    fontSize: 40,
  },
  errorIcon: {
    fontSize: 40,
  },
  emptyTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    marginBottom: Spacing.sm,
  },
  emptySubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Spacing.lg,
  },
  startShoppingButton: {
    marginTop: Spacing.md,
  },
});
