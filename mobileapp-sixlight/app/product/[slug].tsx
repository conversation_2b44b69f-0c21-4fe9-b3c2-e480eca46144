/**
 * Six Light Media Store - Product Detail Screen
 * Detailed product view with customization and add to cart
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
  Image,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter, useLocalSearchParams } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import ProductCustomizer from "@/components/ProductCustomizer";
import { apiClient, Product, formatPrice } from "@/services/api";

interface CustomizationData {
  customColor?: string;
  customText?: string;
  isCustomized: boolean;
  customizationData?: string;
  customizationPreview?: string;
}

export default function ProductDetailScreen() {
  const { colors } = useTheme();
  const { addToCart, isInWishlist, addToWishlist, removeFromWishlist } =
    useApp();
  const router = useRouter();
  const { slug } = useLocalSearchParams<{ slug: string }>();

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);
  const [showCustomizer, setShowCustomizer] = useState(false);
  const [customization, setCustomization] = useState<CustomizationData | null>(
    null
  );

  useEffect(() => {
    if (slug) {
      loadProduct();
    }
  }, [slug]);

  const loadProduct = async () => {
    try {
      setLoading(true);
      const productData = await apiClient.getProductBySlug(slug!);
      setProduct(productData);
    } catch (error) {
      console.error("Failed to load product:", error);
      Alert.alert("Error", "Failed to load product details");
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleQuantityChange = (newQuantity: number) => {
    setQuantity(Math.max(1, newQuantity));
  };

  const handleCustomize = () => {
    if (!product?.customizable) {
      Alert.alert("Info", "This product is not customizable");
      return;
    }
    setShowCustomizer(true);
  };

  const handleCustomizationComplete = (
    customizationData: CustomizationData
  ) => {
    setCustomization(customizationData);
    setShowCustomizer(false);
    Alert.alert("Success", "Customization saved! You can now add to cart.");
  };

  const handleAddToCart = () => {
    if (!product) return;

    if (product.customizable && !customization) {
      Alert.alert(
        "Customization Required",
        "This product requires customization before adding to cart.",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Customize Now", onPress: handleCustomize },
        ]
      );
      return;
    }

    addToCart(product, quantity, customization);
    Alert.alert("Success", `${product.name} added to cart!`);
  };

  const handleToggleWishlist = () => {
    if (!product) return;

    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id);
    } else {
      addToWishlist(product);
    }
  };

  if (loading) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: colors.text.secondary }]}>
            Loading product...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!product) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.text.primary }]}>
            Product not found
          </Text>
          <Button
            title="Go Back"
            onPress={() => router.back()}
            variant="primary"
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Text style={[styles.backButtonText, { color: colors.text.primary }]}>
            ← Back
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleToggleWishlist}
          style={styles.wishlistButton}
        >
          <Text
            style={[
              styles.wishlistButtonText,
              {
                color: isInWishlist(product.id)
                  ? colors.error
                  : colors.text.secondary,
              },
            ]}
          >
            {isInWishlist(product.id) ? "♥" : "♡"}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Product Image */}
        <Card variant="flat" padding="none" style={styles.imageCard}>
          <View
            style={[
              styles.productImage,
              { backgroundColor: colors.background.secondary },
            ]}
          >
            {product.image ? (
              <Image
                source={{ uri: product.image }}
                style={styles.productImageReal}
                resizeMode="cover"
              />
            ) : (
              <View style={styles.imagePlaceholder}>
                <Text
                  style={[
                    styles.placeholderIcon,
                    { color: colors.secondary[500] },
                  ]}
                >
                  📦
                </Text>
                <Text
                  style={[
                    styles.placeholderText,
                    { color: colors.text.secondary },
                  ]}
                >
                  {product.name}
                </Text>
              </View>
            )}
            {product.customizable && (
              <View
                style={[
                  styles.customizableBadgeOverlay,
                  { backgroundColor: colors.primary[500] },
                ]}
              >
                <Text style={styles.customizableBadgeText}>
                  🎨 Customizable
                </Text>
              </View>
            )}
          </View>
        </Card>

        {/* Product Info */}
        <Card variant="default" padding="lg" style={styles.infoCard}>
          <Text style={[styles.productName, { color: colors.text.primary }]}>
            {product.name}
          </Text>
          <Text style={[styles.productPrice, { color: colors.primary[500] }]}>
            {formatPrice(product.price)}
          </Text>
          <Text
            style={[
              styles.productDescription,
              { color: colors.text.secondary },
            ]}
          >
            {product.description}
          </Text>

          {/* Customization Status */}
          {product.customizable && (
            <View style={styles.customizationStatus}>
              {customization ? (
                <Card
                  variant="flat"
                  padding="md"
                  style={[
                    styles.customizationCard,
                    { backgroundColor: colors.primary[50] },
                  ]}
                >
                  <View style={styles.customizationHeader}>
                    <Text
                      style={[
                        styles.customizedText,
                        { color: colors.primary[700] },
                      ]}
                    >
                      ✅ Customization Applied
                    </Text>
                    <TouchableOpacity
                      onPress={handleCustomize}
                      style={styles.editButton}
                    >
                      <Text
                        style={[
                          styles.editCustomization,
                          { color: colors.primary[500] },
                        ]}
                      >
                        🎨 Edit
                      </Text>
                    </TouchableOpacity>
                  </View>
                  {customization.customText && (
                    <View style={styles.customizationPreview}>
                      <Text
                        style={[
                          styles.previewLabel,
                          { color: colors.text.secondary },
                        ]}
                      >
                        Custom Text:
                      </Text>
                      <Text
                        style={[
                          styles.previewText,
                          {
                            color:
                              customization.customColor || colors.text.primary,
                            fontWeight: "bold",
                          },
                        ]}
                      >
                        "{customization.customText}"
                      </Text>
                    </View>
                  )}
                </Card>
              ) : (
                <View
                  style={[
                    styles.customizableBadge,
                    { backgroundColor: colors.secondary[100] },
                  ]}
                >
                  <Text
                    style={[
                      styles.customizableText,
                      { color: colors.secondary[700] },
                    ]}
                  >
                    🎨 Customizable Product - Tap "Customize" to personalize
                  </Text>
                </View>
              )}
            </View>
          )}
        </Card>

        {/* Quantity Selector */}
        <Card variant="default" padding="lg" style={styles.quantityCard}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Quantity
          </Text>
          <View style={styles.quantityControls}>
            <TouchableOpacity
              style={[
                styles.quantityButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={() => handleQuantityChange(quantity - 1)}
            >
              <Text
                style={[
                  styles.quantityButtonText,
                  { color: colors.text.primary },
                ]}
              >
                ➖
              </Text>
            </TouchableOpacity>
            <Text style={[styles.quantity, { color: colors.text.primary }]}>
              {quantity}
            </Text>
            <TouchableOpacity
              style={[
                styles.quantityButton,
                { backgroundColor: colors.primary[500] },
              ]}
              onPress={() => handleQuantityChange(quantity + 1)}
            >
              <Text
                style={[styles.quantityButtonText, { color: colors.white }]}
              >
                ➕
              </Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Actions */}
        <View style={styles.actions}>
          {product.customizable && (
            <Button
              title={
                customization ? "🎨 Edit Customization" : "🎨 Customize Product"
              }
              onPress={handleCustomize}
              variant="outline"
              size="lg"
              fullWidth
              style={styles.actionButton}
            />
          )}
          <Button
            title="🛒 Add to Cart"
            onPress={handleAddToCart}
            variant="primary"
            size="lg"
            fullWidth
            style={styles.actionButton}
          />
        </View>
      </ScrollView>

      {/* Customizer Modal */}
      <Modal
        visible={showCustomizer}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        {product && (
          <ProductCustomizer
            product={product}
            onCustomizationComplete={handleCustomizationComplete}
            onClose={() => setShowCustomizer(false)}
          />
        )}
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  backButton: {
    padding: Spacing.sm,
  },
  backButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  wishlistButton: {
    padding: Spacing.sm,
  },
  wishlistButtonText: {
    fontSize: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  imageCard: {
    marginBottom: Spacing.lg,
    borderRadius: BorderRadius.xl,
    overflow: "hidden",
  },
  productImage: {
    height: 300,
    justifyContent: "center",
    alignItems: "center",
  },
  productImageReal: {
    width: "100%",
    height: "100%",
  },
  imagePlaceholder: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.secondary[100],
    borderRadius: BorderRadius.lg,
    margin: Spacing.lg,
  },
  placeholderIcon: {
    fontSize: 48,
    marginBottom: Spacing.sm,
  },
  placeholderText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
    textAlign: "center",
  },
  customizableBadgeOverlay: {
    position: "absolute",
    top: Spacing.md,
    right: Spacing.md,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
    borderRadius: BorderRadius.sm,
  },
  customizableBadgeText: {
    color: Colors.white,
    fontSize: Typography.fontSize.xs,
    fontWeight: "600",
  },
  infoCard: {
    marginBottom: Spacing.lg,
  },
  productName: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    marginBottom: Spacing.sm,
  },
  productPrice: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    marginBottom: Spacing.md,
  },
  productDescription: {
    fontSize: Typography.fontSize.base,
    lineHeight: 24,
    marginBottom: Spacing.md,
  },
  customizationStatus: {
    marginTop: Spacing.md,
  },
  customizationCard: {
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    borderColor: Colors.primary[200],
  },
  customizationHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Spacing.sm,
  },
  customizedText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  editButton: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.primary[100],
  },
  editCustomization: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
  customizationPreview: {
    marginTop: Spacing.sm,
    paddingTop: Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: Colors.primary[200],
  },
  previewLabel: {
    fontSize: Typography.fontSize.xs,
    fontWeight: "500",
    marginBottom: 2,
  },
  previewText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "bold",
  },
  customizableBadge: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  customizableText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
    textAlign: "center",
  },
  quantityCard: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.md,
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: Spacing.lg,
  },
  quantityButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  quantityButtonText: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  quantity: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    minWidth: 40,
    textAlign: "center",
  },
  actions: {
    gap: Spacing.md,
    paddingBottom: Spacing["3xl"],
  },
  actionButton: {
    marginBottom: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "500",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
  },
  errorText: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    marginBottom: Spacing.lg,
    textAlign: "center",
  },
});
