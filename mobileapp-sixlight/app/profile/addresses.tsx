/**
 * Six Light Media Store - Manage Addresses Screen
 * User shipping addresses management
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';

import { useTheme } from '@/contexts/ThemeContext';
import { Colors, Spacing, BorderRadius, Typography } from '@/constants/Theme';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

interface Address {
  id: string;
  name: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault: boolean;
}

export default function AddressesScreen() {
  const { colors } = useTheme();
  const router = useRouter();

  // Mock addresses data
  const [addresses, setAddresses] = useState<Address[]>([
    {
      id: '1',
      name: 'Home',
      street: '123 Main Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'United States',
      isDefault: true,
    },
    {
      id: '2',
      name: 'Work',
      street: '456 Business Ave',
      city: 'New York',
      state: 'NY',
      zipCode: '10002',
      country: 'United States',
      isDefault: false,
    },
  ]);

  const handleSetDefault = (addressId: string) => {
    setAddresses(prev => 
      prev.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      }))
    );
  };

  const handleDeleteAddress = (addressId: string) => {
    Alert.alert(
      'Delete Address',
      'Are you sure you want to delete this address?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setAddresses(prev => prev.filter(addr => addr.id !== addressId));
          },
        },
      ]
    );
  };

  const renderAddressItem = ({ item }: { item: Address }) => (
    <Card variant="elevated" padding="lg" style={styles.addressCard}>
      <View style={styles.addressHeader}>
        <View style={styles.addressNameContainer}>
          <Text style={[styles.addressName, { color: colors.text.primary }]}>
            {item.name}
          </Text>
          {item.isDefault && (
            <View style={[styles.defaultBadge, { backgroundColor: colors.primary[500] }]}>
              <Text style={styles.defaultBadgeText}>Default</Text>
            </View>
          )}
        </View>
        <TouchableOpacity onPress={() => handleDeleteAddress(item.id)}>
          <Text style={[styles.deleteButton, { color: colors.error }]}>
            Delete
          </Text>
        </TouchableOpacity>
      </View>

      <Text style={[styles.addressText, { color: colors.text.secondary }]}>
        {item.street}
      </Text>
      <Text style={[styles.addressText, { color: colors.text.secondary }]}>
        {item.city}, {item.state} {item.zipCode}
      </Text>
      <Text style={[styles.addressText, { color: colors.text.secondary }]}>
        {item.country}
      </Text>

      <View style={styles.addressActions}>
        <Button
          title="Edit"
          onPress={() => Alert.alert('Coming Soon', 'Edit address feature will be available soon')}
          variant="outline"
          size="sm"
          style={styles.actionButton}
        />
        {!item.isDefault && (
          <Button
            title="Set as Default"
            onPress={() => handleSetDefault(item.id)}
            variant="ghost"
            size="sm"
            style={styles.actionButton}
          />
        )}
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background.primary }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Text style={[styles.backButton, { color: colors.primary[500] }]}>
            ← Back
          </Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          Addresses
        </Text>
        <TouchableOpacity onPress={() => Alert.alert('Coming Soon', 'Add address feature will be available soon')}>
          <Text style={[styles.addButton, { color: colors.primary[500] }]}>
            Add
          </Text>
        </TouchableOpacity>
      </View>

      {/* Addresses List */}
      <FlatList
        data={addresses}
        renderItem={renderAddressItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.addressesList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <View style={[styles.emptyIcon, { backgroundColor: colors.secondary[200] }]} />
            <Text style={[styles.emptyTitle, { color: colors.text.primary }]}>
              No addresses saved
            </Text>
            <Text style={[styles.emptySubtitle, { color: colors.text.secondary }]}>
              Add your first shipping address to get started
            </Text>
            <Button
              title="Add Address"
              onPress={() => Alert.alert('Coming Soon', 'Add address feature will be available soon')}
              variant="primary"
              style={styles.addFirstButton}
            />
          </View>
        }
      />

      {/* Add Address Button */}
      {addresses.length > 0 && (
        <View style={styles.addButtonContainer}>
          <Button
            title="Add New Address"
            onPress={() => Alert.alert('Coming Soon', 'Add address feature will be available soon')}
            variant="outline"
            size="lg"
            fullWidth
          />
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  backButton: {
    fontSize: Typography.fontSize.base,
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: 'bold',
  },
  addButton: {
    fontSize: Typography.fontSize.base,
    fontWeight: '600',
  },
  addressesList: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing['3xl'],
  },
  addressCard: {
    marginBottom: Spacing.md,
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  addressNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  addressName: {
    fontSize: Typography.fontSize.lg,
    fontWeight: 'bold',
  },
  defaultBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: 2,
    borderRadius: BorderRadius.sm,
  },
  defaultBadgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: 'bold',
    color: Colors.white,
  },
  deleteButton: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '600',
  },
  addressText: {
    fontSize: Typography.fontSize.base,
    marginBottom: Spacing.xs,
  },
  addressActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
    marginTop: Spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing['3xl'],
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: Spacing.lg,
  },
  emptyTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: 'bold',
    marginBottom: Spacing.sm,
  },
  emptySubtitle: {
    fontSize: Typography.fontSize.base,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  addFirstButton: {
    marginTop: Spacing.md,
  },
  addButtonContainer: {
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.lg,
  },
});
