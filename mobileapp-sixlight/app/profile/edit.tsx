/**
 * Six Light Media Store - Edit Profile Screen
 * Responsive user profile editing with advanced form validation and UX
 */

import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  Animated,
  Image,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import * as ImagePicker from "expo-image-picker";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";

const { width: screenWidth } = Dimensions.get("window");

interface FormData {
  name: string;
  email: string;
  phone: string;
  bio: string;
}

interface FormErrors {
  name?: string;
  email?: string;
  phone?: string;
  bio?: string;
}

export default function EditProfileScreen() {
  const { colors } = useTheme();
  const { state } = useApp();
  const router = useRouter();

  const [formData, setFormData] = useState<FormData>({
    name: state.user?.name || "",
    email: state.user?.email || "",
    phone: state.user?.phone || "",
    bio: state.user?.bio || "",
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Input refs for navigation
  const nameRef = useRef<TextInput>(null);
  const emailRef = useRef<TextInput>(null);
  const phoneRef = useRef<TextInput>(null);
  const bioRef = useRef<TextInput>(null);

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phone === "" || phoneRegex.test(phone.replace(/\s/g, ""));
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Name must be at least 2 characters";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (formData.phone && !validatePhone(formData.phone)) {
      newErrors.phone = "Please enter a valid phone number";
    }

    if (formData.bio && formData.bio.length > 500) {
      newErrors.bio = "Bio must be less than 500 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setHasChanges(true);

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleImagePicker = async () => {
    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please grant camera roll permissions to change your profile picture."
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setProfileImage(result.assets[0].uri);
        setHasChanges(true);
      }
    } catch (error) {
      Alert.alert("Error", "Failed to pick image");
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement API call to update profile
      const updateData = {
        ...formData,
        profileImage: profileImage,
      };

      await new Promise((resolve) => setTimeout(resolve, 1500)); // Simulate API call

      Alert.alert("Success", "Profile updated successfully", [
        { text: "OK", onPress: () => router.back() },
      ]);
      setHasChanges(false);
    } catch (error) {
      Alert.alert("Error", "Failed to update profile. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleDiscard = () => {
    if (hasChanges) {
      Alert.alert(
        "Discard Changes",
        "Are you sure you want to discard your changes?",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Discard",
            style: "destructive",
            onPress: () => router.back(),
          },
        ]
      );
    } else {
      router.back();
    }
  };

  // Custom Input Component
  const CustomInput = ({
    label,
    value,
    onChangeText,
    error,
    placeholder,
    keyboardType = "default",
    autoCapitalize = "words",
    multiline = false,
    numberOfLines = 1,
    inputRef,
    onSubmitEditing,
    returnKeyType = "next",
  }: {
    label: string;
    value: string;
    onChangeText: (text: string) => void;
    error?: string;
    placeholder: string;
    keyboardType?: any;
    autoCapitalize?: any;
    multiline?: boolean;
    numberOfLines?: number;
    inputRef?: React.RefObject<TextInput>;
    onSubmitEditing?: () => void;
    returnKeyType?: any;
  }) => (
    <View style={styles.inputGroup}>
      <Text style={[styles.inputLabel, { color: colors.text.secondary }]}>
        {label}
      </Text>
      <TextInput
        ref={inputRef}
        style={[
          multiline ? styles.textArea : styles.input,
          {
            backgroundColor: colors.background.secondary,
            color: colors.text.primary,
            borderColor: error
              ? colors.error
              : focusedField === label.toLowerCase()
              ? colors.primary[500]
              : colors.border.light,
            borderWidth: focusedField === label.toLowerCase() ? 2 : 1,
          },
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={colors.text.tertiary}
        keyboardType={keyboardType}
        autoCapitalize={autoCapitalize}
        multiline={multiline}
        numberOfLines={numberOfLines}
        textAlignVertical={multiline ? "top" : "center"}
        onFocus={() => setFocusedField(label.toLowerCase())}
        onBlur={() => setFocusedField(null)}
        onSubmitEditing={onSubmitEditing}
        returnKeyType={returnKeyType}
      />
      {error && (
        <Animated.View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.error }]}>
            {error}
          </Text>
        </Animated.View>
      )}
    </View>
  );

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleDiscard}>
          <Text style={[styles.backButton, { color: colors.primary[500] }]}>
            ← {hasChanges ? "Discard" : "Back"}
          </Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          Edit Profile
        </Text>
        {hasChanges && (
          <TouchableOpacity onPress={handleSave} disabled={loading}>
            <Text
              style={[
                styles.saveButton,
                {
                  color: loading ? colors.text.tertiary : colors.primary[500],
                },
              ]}
            >
              Save
            </Text>
          </TouchableOpacity>
        )}
        {!hasChanges && <View style={styles.headerSpacer} />}
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
      >
        <Animated.View
          style={[
            styles.animatedContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Profile Picture Section */}
            <Card variant="elevated" padding="lg" style={styles.section}>
              <Text
                style={[styles.sectionTitle, { color: colors.text.primary }]}
              >
                Profile Picture
              </Text>
              <View style={styles.avatarSection}>
                <TouchableOpacity
                  style={[
                    styles.avatarContainer,
                    {
                      backgroundColor: profileImage
                        ? "transparent"
                        : colors.secondary[200],
                    },
                  ]}
                  onPress={handleImagePicker}
                >
                  {profileImage ? (
                    <Image
                      source={{ uri: profileImage }}
                      style={styles.avatarImage}
                    />
                  ) : (
                    <View style={styles.defaultAvatarContainer}>
                      <Text
                        style={[
                          styles.userIcon,
                          { color: colors.secondary[500] },
                        ]}
                      >
                        👤
                      </Text>
                    </View>
                  )}
                  <View
                    style={[
                      styles.cameraIcon,
                      { backgroundColor: colors.primary[500] },
                    ]}
                  >
                    <Text style={styles.cameraIconText}>📷</Text>
                  </View>
                </TouchableOpacity>
                <View style={styles.avatarInfo}>
                  <Text
                    style={[styles.avatarTitle, { color: colors.text.primary }]}
                  >
                    Profile Photo
                  </Text>
                  <Text
                    style={[
                      styles.avatarSubtitle,
                      { color: colors.text.secondary },
                    ]}
                  >
                    Tap to change your profile picture
                  </Text>
                  <Button
                    title="Choose Photo"
                    onPress={handleImagePicker}
                    variant="outline"
                    size="sm"
                    style={styles.changePhotoButton}
                  />
                </View>
              </View>
            </Card>

            {/* Personal Information */}
            <Card variant="elevated" padding="lg" style={styles.section}>
              <Text
                style={[styles.sectionTitle, { color: colors.text.primary }]}
              >
                Personal Information
              </Text>

              <CustomInput
                label="Full Name *"
                value={formData.name}
                onChangeText={(value) => handleInputChange("name", value)}
                error={errors.name}
                placeholder="Enter your full name"
                inputRef={nameRef}
                onSubmitEditing={() => emailRef.current?.focus()}
                returnKeyType="next"
              />

              <CustomInput
                label="Email Address *"
                value={formData.email}
                onChangeText={(value) => handleInputChange("email", value)}
                error={errors.email}
                placeholder="Enter your email address"
                keyboardType="email-address"
                autoCapitalize="none"
                inputRef={emailRef}
                onSubmitEditing={() => phoneRef.current?.focus()}
                returnKeyType="next"
              />

              <CustomInput
                label="Phone Number"
                value={formData.phone}
                onChangeText={(value) => handleInputChange("phone", value)}
                error={errors.phone}
                placeholder="Enter your phone number"
                keyboardType="phone-pad"
                inputRef={phoneRef}
                onSubmitEditing={() => bioRef.current?.focus()}
                returnKeyType="next"
              />

              <CustomInput
                label="Bio"
                value={formData.bio}
                onChangeText={(value) => handleInputChange("bio", value)}
                error={errors.bio}
                placeholder="Tell us about yourself (optional)"
                multiline={true}
                numberOfLines={4}
                inputRef={bioRef}
                returnKeyType="done"
              />

              {/* Character count for bio */}
              <View style={styles.characterCount}>
                <Text
                  style={[
                    styles.characterCountText,
                    {
                      color:
                        formData.bio.length > 450
                          ? colors.warning
                          : colors.text.tertiary,
                    },
                  ]}
                >
                  {formData.bio.length}/500 characters
                </Text>
              </View>
            </Card>

            {/* Action Buttons */}
            <View style={styles.actionButtonsContainer}>
              <Button
                title="Save Changes"
                onPress={handleSave}
                variant="primary"
                size="lg"
                fullWidth
                loading={loading}
                disabled={!hasChanges || loading}
                style={[styles.saveButton, { opacity: hasChanges ? 1 : 0.6 }]}
              />

              {hasChanges && (
                <Button
                  title="Reset Changes"
                  onPress={() => {
                    setFormData({
                      name: state.user?.name || "",
                      email: state.user?.email || "",
                      phone: state.user?.phone || "",
                      bio: state.user?.bio || "",
                    });
                    setProfileImage(null);
                    setErrors({});
                    setHasChanges(false);
                  }}
                  variant="ghost"
                  size="md"
                  fullWidth
                  style={styles.resetButton}
                />
              )}
            </View>

            <View style={styles.bottomSpacing} />
          </ScrollView>
        </Animated.View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  animatedContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.secondary[200],
  },
  backButton: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    minWidth: 60,
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    flex: 1,
    textAlign: "center",
  },
  saveButton: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    minWidth: 60,
    textAlign: "right",
  },
  headerSpacer: {
    width: 60,
  },
  section: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.md,
  },
  avatarSection: {
    flexDirection: screenWidth > 400 ? "row" : "column",
    alignItems: "center",
    gap: Spacing.lg,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  avatarImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarText: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: "bold",
    color: Colors.white,
  },
  defaultAvatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  userIcon: {
    fontSize: 48,
  },
  cameraIcon: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: Colors.white,
  },
  cameraIconText: {
    fontSize: 16,
  },
  avatarInfo: {
    flex: screenWidth > 400 ? 1 : undefined,
    alignItems: screenWidth > 400 ? "flex-start" : "center",
  },
  avatarTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  avatarSubtitle: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Spacing.md,
    textAlign: screenWidth > 400 ? "left" : "center",
  },
  changePhotoButton: {
    minWidth: 120,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  inputLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
    marginBottom: Spacing.sm,
  },
  input: {
    borderWidth: 1,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Platform.OS === "ios" ? Spacing.md : Spacing.sm,
    fontSize: Typography.fontSize.base,
    minHeight: 48,
    textAlignVertical: "center",
  },
  textArea: {
    borderWidth: 1,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    fontSize: Typography.fontSize.base,
    minHeight: 120,
    maxHeight: 200,
    textAlignVertical: "top",
  },
  errorContainer: {
    marginTop: Spacing.xs,
  },
  errorText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "500",
  },
  characterCount: {
    alignItems: "flex-end",
    marginTop: -Spacing.sm,
    marginBottom: Spacing.sm,
  },
  characterCountText: {
    fontSize: Typography.fontSize.xs,
  },
  actionButtonsContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
    gap: Spacing.md,
  },
  resetButton: {
    marginTop: Spacing.sm,
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
});
