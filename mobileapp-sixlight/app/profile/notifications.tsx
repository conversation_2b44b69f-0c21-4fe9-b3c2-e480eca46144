/**
 * Six Light Media Store - Notification Settings Screen
 * User notification preferences management
 */

import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";

interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
  category: "orders" | "marketing" | "security" | "app";
}

export default function NotificationSettingsScreen() {
  const { colors } = useTheme();
  const router = useRouter();

  const [settings, setSettings] = useState<NotificationSetting[]>([
    // Order Notifications
    {
      id: "order_updates",
      title: "Order Updates",
      description: "Get notified about order status changes",
      enabled: true,
      category: "orders",
    },
    {
      id: "order_shipped",
      title: "Shipping Notifications",
      description: "When your order has been shipped",
      enabled: true,
      category: "orders",
    },
    {
      id: "order_delivered",
      title: "Delivery Confirmations",
      description: "When your order has been delivered",
      enabled: true,
      category: "orders",
    },

    // Marketing Notifications
    {
      id: "promotions",
      title: "Promotions & Offers",
      description: "Special deals and discount notifications",
      enabled: false,
      category: "marketing",
    },
    {
      id: "new_products",
      title: "New Products",
      description: "Be the first to know about new arrivals",
      enabled: false,
      category: "marketing",
    },
    {
      id: "recommendations",
      title: "Product Recommendations",
      description: "Personalized product suggestions",
      enabled: true,
      category: "marketing",
    },

    // Security Notifications
    {
      id: "security_alerts",
      title: "Security Alerts",
      description: "Important account security notifications",
      enabled: true,
      category: "security",
    },
    {
      id: "login_alerts",
      title: "Login Notifications",
      description: "When someone logs into your account",
      enabled: true,
      category: "security",
    },

    // App Notifications
    {
      id: "app_updates",
      title: "App Updates",
      description: "New features and app improvements",
      enabled: false,
      category: "app",
    },
  ]);

  const [loading, setLoading] = useState(false);

  const toggleSetting = (settingId: string) => {
    setSettings((prev) =>
      prev.map((setting) =>
        setting.id === settingId
          ? { ...setting, enabled: !setting.enabled }
          : setting
      )
    );
  };

  const handleSaveSettings = async () => {
    setLoading(true);
    try {
      // TODO: Implement API call to save notification settings
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API call

      Alert.alert("Success", "Notification settings saved successfully");
    } catch (error) {
      Alert.alert("Error", "Failed to save notification settings");
    } finally {
      setLoading(false);
    }
  };

  const getCategoryTitle = (category: string) => {
    switch (category) {
      case "orders":
        return "Order Notifications";
      case "marketing":
        return "Marketing & Promotions";
      case "security":
        return "Security & Account";
      case "app":
        return "App Notifications";
      default:
        return "Other";
    }
  };

  const groupedSettings = settings.reduce((acc, setting) => {
    if (!acc[setting.category]) {
      acc[setting.category] = [];
    }
    acc[setting.category].push(setting);
    return acc;
  }, {} as Record<string, NotificationSetting[]>);

  const renderNotificationItem = (
    setting: NotificationSetting,
    isLast: boolean = false
  ) => (
    <View
      key={setting.id}
      style={[
        styles.notificationItem,
        !isLast && {
          borderBottomWidth: 1,
          borderBottomColor: colors.border.light,
        },
      ]}
    >
      <View style={styles.notificationContent}>
        <Text
          style={[styles.notificationTitle, { color: colors.text.primary }]}
        >
          {setting.title}
        </Text>
        <Text
          style={[
            styles.notificationDescription,
            { color: colors.text.secondary },
          ]}
        >
          {setting.description}
        </Text>
      </View>
      <Switch
        value={setting.enabled}
        onValueChange={() => toggleSetting(setting.id)}
        trackColor={{
          false: colors.secondary[300],
          true: colors.primary[200],
        }}
        thumbColor={
          setting.enabled ? colors.primary[500] : colors.secondary[500]
        }
        ios_backgroundColor={colors.secondary[300]}
      />
    </View>
  );

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Text style={[styles.backButton, { color: colors.primary[500] }]}>
            ← Back
          </Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          Notifications
        </Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Notification Categories */}
        {Object.entries(groupedSettings).map(([category, categorySettings]) => (
          <Card
            key={category}
            variant="elevated"
            padding="lg"
            style={styles.categoryCard}
          >
            <Text
              style={[styles.categoryTitle, { color: colors.text.primary }]}
            >
              {getCategoryTitle(category)}
            </Text>
            <Text
              style={[
                styles.categoryDescription,
                { color: colors.text.secondary },
              ]}
            >
              {category === "orders" &&
                "Stay updated on your order status and delivery"}
              {category === "marketing" &&
                "Receive promotions and product updates"}
              {category === "security" &&
                "Important security and account alerts"}
              {category === "app" && "App updates and new features"}
            </Text>
            {categorySettings.map((setting, index) =>
              renderNotificationItem(
                setting,
                index === categorySettings.length - 1
              )
            )}
          </Card>
        ))}

        {/* Quick Actions */}
        <Card variant="elevated" padding="lg" style={styles.categoryCard}>
          <Text style={[styles.categoryTitle, { color: colors.text.primary }]}>
            Quick Actions
          </Text>

          <View style={styles.quickActions}>
            <Button
              title="Enable All"
              onPress={() => {
                setSettings((prev) =>
                  prev.map((setting) => ({ ...setting, enabled: true }))
                );
              }}
              variant="outline"
              size="sm"
              style={styles.quickActionButton}
            />
            <Button
              title="Disable All"
              onPress={() => {
                setSettings((prev) =>
                  prev.map((setting) => ({ ...setting, enabled: false }))
                );
              }}
              variant="ghost"
              size="sm"
              style={styles.quickActionButton}
            />
          </View>
        </Card>

        {/* Save Button */}
        <View style={styles.saveButtonContainer}>
          <Button
            title="Save Settings"
            onPress={handleSaveSettings}
            variant="primary"
            size="lg"
            fullWidth
            loading={loading}
          />
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  backButton: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  headerSpacer: {
    width: 50,
  },
  categoryCard: {
    marginHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  categoryTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.xs,
  },
  categoryDescription: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  notificationItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: Spacing.md,
  },
  notificationContent: {
    flex: 1,
    marginRight: Spacing.md,
  },
  notificationTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  notificationDescription: {
    fontSize: Typography.fontSize.sm,
    lineHeight: 20,
  },
  quickActions: {
    flexDirection: "row",
    gap: Spacing.md,
  },
  quickActionButton: {
    flex: 1,
  },
  saveButtonContainer: {
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
});
