/**
 * Six Light Media Store - Mobile Product Customizer
 * Mobile-optimized product customization interface
 */

import React, { useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Dimensions,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";

import { useTheme } from "@/contexts/ThemeContext";
import { useApp } from "@/contexts/AppContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { Product } from "@/services/api";

const { width, height } = Dimensions.get("window");

interface ProductCustomizerProps {
  product: Product;
  onCustomizationComplete: (customizationData: CustomizationData) => void;
  onClose: () => void;
}

interface CustomizationData {
  customColor?: string;
  customText?: string;
  isCustomized: boolean;
  customizationData?: string; // JSON string for complex customizations
  customizationPreview?: string; // Base64 image preview
}

const PRESET_COLORS = [
  "#FF6B6B",
  "#4ECDC4",
  "#45B7D1",
  "#96CEB4",
  "#FFEAA7",
  "#DDA0DD",
  "#98D8C8",
  "#F7DC6F",
  "#BB8FCE",
  "#85C1E9",
  "#F8C471",
  "#82E0AA",
  "#F1948A",
  "#85C1E9",
  "#D7BDE2",
];

export default function ProductCustomizer({
  product,
  onCustomizationComplete,
  onClose,
}: ProductCustomizerProps) {
  const { colors } = useTheme();
  const router = useRouter();

  const [customText, setCustomText] = useState("");
  const [selectedColor, setSelectedColor] = useState(PRESET_COLORS[0]);
  const [fontSize, setFontSize] = useState(24);
  const [textPosition, setTextPosition] = useState({ x: 50, y: 50 });
  const [selectedFont, setSelectedFont] = useState("bold");
  const [textStyle, setTextStyle] = useState<"normal" | "italic" | "bold">(
    "bold"
  );

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
  };

  const handleTextChange = (text: string) => {
    setCustomText(text);
  };

  const handleFontSizeChange = (size: number) => {
    setFontSize(Math.max(12, Math.min(48, size)));
  };

  const handleSaveCustomization = () => {
    if (!customText.trim()) {
      Alert.alert("Error", "Please enter some text for customization");
      return;
    }

    const customizationData: CustomizationData = {
      customColor: selectedColor,
      customText: customText.trim(),
      isCustomized: true,
      customizationData: JSON.stringify({
        text: customText.trim(),
        color: selectedColor,
        fontSize,
        position: textPosition,
        textStyle,
        product: product.name,
        timestamp: new Date().toISOString(),
      }),
      // In a real app, you would generate a preview image here
      customizationPreview: "", // Base64 image would go here
    };

    onCustomizationComplete(customizationData);
  };

  const handleReset = () => {
    setCustomText("");
    setSelectedColor(PRESET_COLORS[0]);
    setFontSize(24);
    setTextPosition({ x: 50, y: 50 });
    setTextStyle("bold");
  };

  const getTextStyleProps = () => {
    return {
      fontWeight: textStyle === "bold" ? "bold" : "normal",
      fontStyle: textStyle === "italic" ? "italic" : "normal",
    };
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Text
            style={[styles.closeButtonText, { color: colors.text.secondary }]}
          >
            Cancel
          </Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          🎨 Customize {product.name}
        </Text>
        <TouchableOpacity onPress={handleReset} style={styles.resetButton}>
          <Text
            style={[styles.resetButtonText, { color: colors.primary[500] }]}
          >
            Reset
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Product Preview */}
        <Card variant="elevated" padding="lg" style={styles.previewCard}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Preview
          </Text>
          <View
            style={[
              styles.previewContainer,
              { backgroundColor: colors.background.secondary },
            ]}
          >
            {/* Product Base */}
            <View
              style={[
                styles.productBase,
                { backgroundColor: colors.secondary[200] },
              ]}
            >
              {/* Custom Text Overlay */}
              {customText.trim() && (
                <View
                  style={[
                    styles.textOverlay,
                    {
                      left: `${textPosition.x}%`,
                      top: `${textPosition.y}%`,
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.customTextPreview,
                      {
                        color: selectedColor,
                        fontSize: fontSize * 0.8, // Scale down for preview
                        ...getTextStyleProps(),
                      },
                    ]}
                  >
                    {customText}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </Card>

        {/* Text Input */}
        <Card variant="default" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Custom Text
          </Text>
          <TextInput
            style={[
              styles.textInput,
              {
                backgroundColor: colors.background.secondary,
                color: colors.text.primary,
                borderColor: colors.border.light,
              },
            ]}
            placeholder="Enter your custom text..."
            placeholderTextColor={colors.text.tertiary}
            value={customText}
            onChangeText={handleTextChange}
            maxLength={50}
            multiline
          />
          <Text
            style={[styles.characterCount, { color: colors.text.secondary }]}
          >
            {customText.length}/50 characters
          </Text>
        </Card>

        {/* Color Selection */}
        <Card variant="default" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Text Color
          </Text>
          <View style={styles.colorGrid}>
            {PRESET_COLORS.map((color, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.colorOption,
                  { backgroundColor: color },
                  selectedColor === color && styles.selectedColor,
                ]}
                onPress={() => handleColorSelect(color)}
              >
                {selectedColor === color && (
                  <View style={styles.colorCheckmark}>
                    <Text style={styles.checkmarkText}>✓</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        {/* Text Style */}
        <Card variant="default" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Text Style
          </Text>
          <View style={styles.textStyleControls}>
            <TouchableOpacity
              style={[
                styles.textStyleButton,
                {
                  backgroundColor:
                    textStyle === "normal"
                      ? colors.primary[500]
                      : colors.background.secondary,
                },
              ]}
              onPress={() => setTextStyle("normal")}
            >
              <Text
                style={[
                  styles.textStyleButtonText,
                  {
                    color:
                      textStyle === "normal"
                        ? colors.white
                        : colors.text.primary,
                  },
                ]}
              >
                Normal
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.textStyleButton,
                {
                  backgroundColor:
                    textStyle === "bold"
                      ? colors.primary[500]
                      : colors.background.secondary,
                },
              ]}
              onPress={() => setTextStyle("bold")}
            >
              <Text
                style={[
                  styles.textStyleButtonText,
                  {
                    color:
                      textStyle === "bold" ? colors.white : colors.text.primary,
                    fontWeight: "bold",
                  },
                ]}
              >
                Bold
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.textStyleButton,
                {
                  backgroundColor:
                    textStyle === "italic"
                      ? colors.primary[500]
                      : colors.background.secondary,
                },
              ]}
              onPress={() => setTextStyle("italic")}
            >
              <Text
                style={[
                  styles.textStyleButtonText,
                  {
                    color:
                      textStyle === "italic"
                        ? colors.white
                        : colors.text.primary,
                    fontStyle: "italic",
                  },
                ]}
              >
                Italic
              </Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Font Size */}
        <Card variant="default" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Font Size: {fontSize}px
          </Text>
          <View style={styles.fontSizeControls}>
            <TouchableOpacity
              style={[
                styles.fontSizeButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={() => handleFontSizeChange(fontSize - 2)}
            >
              <Text
                style={[
                  styles.fontSizeButtonText,
                  { color: colors.text.primary },
                ]}
              >
                A-
              </Text>
            </TouchableOpacity>

            <View style={styles.fontSizeSlider}>
              <View
                style={[
                  styles.fontSizeIndicator,
                  {
                    backgroundColor: colors.primary[500],
                    left: `${((fontSize - 12) / (48 - 12)) * 100}%`,
                  },
                ]}
              />
            </View>

            <TouchableOpacity
              style={[
                styles.fontSizeButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={() => handleFontSizeChange(fontSize + 2)}
            >
              <Text
                style={[
                  styles.fontSizeButtonText,
                  { color: colors.text.primary },
                ]}
              >
                A+
              </Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Position Controls */}
        <Card variant="default" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Text Position
          </Text>
          <View style={styles.positionControls}>
            <TouchableOpacity
              style={[
                styles.positionButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={() => setTextPosition({ x: 25, y: 25 })}
            >
              <Text
                style={[
                  styles.positionButtonText,
                  { color: colors.text.primary },
                ]}
              >
                Top Left
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.positionButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={() => setTextPosition({ x: 50, y: 25 })}
            >
              <Text
                style={[
                  styles.positionButtonText,
                  { color: colors.text.primary },
                ]}
              >
                Top Center
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.positionButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={() => setTextPosition({ x: 75, y: 25 })}
            >
              <Text
                style={[
                  styles.positionButtonText,
                  { color: colors.text.primary },
                ]}
              >
                Top Right
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.positionButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={() => setTextPosition({ x: 50, y: 50 })}
            >
              <Text
                style={[
                  styles.positionButtonText,
                  { color: colors.text.primary },
                ]}
              >
                Center
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.positionButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={() => setTextPosition({ x: 25, y: 75 })}
            >
              <Text
                style={[
                  styles.positionButtonText,
                  { color: colors.text.primary },
                ]}
              >
                Bottom Left
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.positionButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={() => setTextPosition({ x: 50, y: 75 })}
            >
              <Text
                style={[
                  styles.positionButtonText,
                  { color: colors.text.primary },
                ]}
              >
                Bottom Center
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.positionButton,
                { backgroundColor: colors.background.secondary },
              ]}
              onPress={() => setTextPosition({ x: 75, y: 75 })}
            >
              <Text
                style={[
                  styles.positionButtonText,
                  { color: colors.text.primary },
                ]}
              >
                Bottom Right
              </Text>
            </TouchableOpacity>
          </View>
        </Card>
      </ScrollView>

      {/* Save Button */}
      <View
        style={[styles.footer, { backgroundColor: colors.background.primary }]}
      >
        <Button
          title={
            customText.trim()
              ? "✅ Save Customization"
              : "Enter text to continue"
          }
          onPress={handleSaveCustomization}
          variant="primary"
          size="lg"
          fullWidth
          disabled={!customText.trim()}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  closeButton: {
    padding: Spacing.sm,
  },
  closeButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  resetButton: {
    padding: Spacing.sm,
  },
  resetButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  section: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.md,
  },
  previewCard: {
    marginTop: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  previewContainer: {
    height: 200,
    borderRadius: BorderRadius.lg,
    overflow: "hidden",
    position: "relative",
  },
  productBase: {
    flex: 1,
    borderRadius: BorderRadius.md,
    margin: Spacing.md,
    position: "relative",
  },
  textOverlay: {
    position: "absolute",
    transform: [{ translateX: -50 }, { translateY: -50 }],
  },
  customTextPreview: {
    fontWeight: "bold",
    textAlign: "center",
    textShadowColor: "rgba(0,0,0,0.3)",
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    fontSize: Typography.fontSize.base,
    minHeight: 80,
    textAlignVertical: "top",
  },
  characterCount: {
    fontSize: Typography.fontSize.sm,
    textAlign: "right",
    marginTop: Spacing.xs,
  },
  colorGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.sm,
  },
  colorOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "transparent",
  },
  selectedColor: {
    borderColor: Colors.white,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  colorCheckmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(255,255,255,0.9)",
    justifyContent: "center",
    alignItems: "center",
  },
  checkmarkText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "bold",
  },
  fontSizeControls: {
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.md,
  },
  fontSizeButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  fontSizeButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "bold",
  },
  fontSizeSlider: {
    flex: 1,
    height: 4,
    backgroundColor: Colors.secondary[200],
    borderRadius: 2,
    position: "relative",
  },
  fontSizeIndicator: {
    position: "absolute",
    width: 20,
    height: 20,
    borderRadius: 10,
    top: -8,
    transform: [{ translateX: -10 }],
  },
  positionControls: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Spacing.sm,
  },
  positionButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    minWidth: 80,
  },
  positionButtonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "500",
    textAlign: "center",
  },
  textStyleControls: {
    flexDirection: "row",
    gap: Spacing.sm,
  },
  textStyleButton: {
    flex: 1,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.md,
    alignItems: "center",
  },
  textStyleButtonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "500",
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },
});
