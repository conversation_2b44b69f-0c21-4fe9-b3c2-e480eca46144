/**
 * Six Light Media Store - Enhanced Splash Screen
 * Extraordinary animated splash screen with brand elements
 */

import React, { useEffect, useRef } from "react";
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
} from "react-native";
import { Image } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Colors } from "@/constants/Theme";

const { width, height } = Dimensions.get("window");

interface SplashScreenProps {
  onAnimationComplete?: () => void;
}

export default function SplashScreen({
  onAnimationComplete,
}: SplashScreenProps) {
  // Animation values
  const logoScale = useRef(new Animated.Value(0.3)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const backgroundOpacity = useRef(new Animated.Value(0)).current;
  const shimmerTranslateX = useRef(new Animated.Value(-width)).current;

  useEffect(() => {
    startAnimation();
  }, []);

  const startAnimation = () => {
    // Background fade in
    Animated.timing(backgroundOpacity, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();

    // Logo entrance animation
    Animated.parallel([
      Animated.timing(logoOpacity, {
        toValue: 1,
        duration: 800,
        delay: 300,
        useNativeDriver: true,
      }),
      Animated.spring(logoScale, {
        toValue: 1,
        delay: 300,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Shimmer effect
    Animated.loop(
      Animated.timing(shimmerTranslateX, {
        toValue: width,
        duration: 1500,
        useNativeDriver: true,
      })
    ).start();

    // Complete animation after delay
    setTimeout(() => {
      if (onAnimationComplete) {
        onAnimationComplete();
      }
    }, 2500);
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={Colors.primary[500]}
      />

      {/* Animated Background */}
      <Animated.View
        style={[styles.backgroundContainer, { opacity: backgroundOpacity }]}
      >
        <LinearGradient
          colors={[
            Colors.primary[600],
            Colors.primary[500],
            Colors.primary[400],
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.gradient}
        />
      </Animated.View>

      {/* Floating Elements */}
      <View style={styles.floatingElements}>
        {[...Array(6)].map((_, index) => (
          <FloatingElement key={index} index={index} />
        ))}
      </View>

      {/* Main Logo Container */}
      <View style={styles.logoContainer}>
        <Animated.View
          style={[
            styles.logoWrapper,
            {
              opacity: logoOpacity,
              transform: [{ scale: logoScale }],
            },
          ]}
        >
          {/* Logo Background Circle */}
          <View style={styles.logoBackground}>
            <View style={styles.logoBackgroundInner} />
          </View>

          {/* Six Light Logo */}
          <Image
            source={require("@/assets/images/6 Light Logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />

          {/* Shimmer Effect */}
          <Animated.View
            style={[
              styles.shimmer,
              {
                transform: [{ translateX: shimmerTranslateX }],
              },
            ]}
          />
        </Animated.View>

        {/* Brand Text */}
        <Animated.Text style={[styles.brandText, { opacity: logoOpacity }]}>
          Six Light Media
        </Animated.Text>

        <Animated.Text style={[styles.taglineText, { opacity: logoOpacity }]}>
          Premium Custom Products
        </Animated.Text>
      </View>

      {/* Loading Indicator */}
      <View style={styles.loadingContainer}>
        <LoadingDots />
      </View>
    </View>
  );
}

// Floating background elements
function FloatingElement({ index }: { index: number }) {
  const translateY = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(0.1)).current;

  useEffect(() => {
    const animateElement = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(translateY, {
            toValue: -30,
            duration: 2000 + index * 200,
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: 30,
            duration: 2000 + index * 200,
            useNativeDriver: true,
          }),
        ])
      ).start();

      Animated.loop(
        Animated.sequence([
          Animated.timing(opacity, {
            toValue: 0.3,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0.1,
            duration: 1500,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    setTimeout(animateElement, index * 300);
  }, []);

  const size = 20 + index * 10;
  const left = (index * width) / 6;
  const top = (index * height) / 8;

  return (
    <Animated.View
      style={[
        styles.floatingElement,
        {
          width: size,
          height: size,
          left,
          top,
          opacity,
          transform: [{ translateY }],
        },
      ]}
    />
  );
}

// Loading dots animation
function LoadingDots() {
  const dot1 = useRef(new Animated.Value(0)).current;
  const dot2 = useRef(new Animated.Value(0)).current;
  const dot3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animateDots = () => {
      const createDotAnimation = (dot: Animated.Value, delay: number) =>
        Animated.loop(
          Animated.sequence([
            Animated.timing(dot, {
              toValue: 1,
              duration: 400,
              delay,
              useNativeDriver: true,
            }),
            Animated.timing(dot, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ])
        );

      Animated.parallel([
        createDotAnimation(dot1, 0),
        createDotAnimation(dot2, 200),
        createDotAnimation(dot3, 400),
      ]).start();
    };

    setTimeout(animateDots, 1000);
  }, []);

  return (
    <View style={styles.dotsContainer}>
      {[dot1, dot2, dot3].map((dot, index) => (
        <Animated.View
          key={index}
          style={[
            styles.dot,
            {
              opacity: dot,
              transform: [
                {
                  scale: dot.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.8, 1.2],
                  }),
                },
              ],
            },
          ]}
        />
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.primary[500],
  },
  backgroundContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  gradient: {
    flex: 1,
  },
  floatingElements: {
    ...StyleSheet.absoluteFillObject,
  },
  floatingElement: {
    position: "absolute",
    backgroundColor: Colors.white,
    borderRadius: 50,
  },
  logoContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
  },
  logoWrapper: {
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 30,
  },
  logoBackground: {
    position: "absolute",
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: Colors.white,
    opacity: 0.1,
    justifyContent: "center",
    alignItems: "center",
  },
  logoBackgroundInner: {
    width: 160,
    height: 160,
    borderRadius: 80,
    backgroundColor: Colors.white,
    opacity: 0.1,
  },
  logo: {
    width: 120,
    height: 120,
    zIndex: 2,
  },
  shimmer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.white,
    opacity: 0.3,
    width: 50,
  },
  brandText: {
    fontSize: 28,
    fontWeight: "bold",
    color: Colors.white,
    textAlign: "center",
    marginBottom: 8,
  },
  taglineText: {
    fontSize: 16,
    color: Colors.white,
    opacity: 0.9,
    textAlign: "center",
  },
  loadingContainer: {
    position: "absolute",
    bottom: 100,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  dotsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.white,
    marginHorizontal: 4,
  },
});
