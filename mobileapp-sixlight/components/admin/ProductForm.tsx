/**
 * Six Light Media Store - Mobile Product Form
 * Add/Edit product form with ImageKit integration
 */

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  Switch,
  Image,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import * as ImagePicker from "expo-image-picker";

import { useTheme } from "@/contexts/ThemeContext";
import { Colors, Spacing, BorderRadius, Typography } from "@/constants/Theme";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import { Product, apiClient } from "@/services/api";
import { uploadImageFromUri } from "@/services/imagekit";

interface ProductFormProps {
  product?: Product | null;
  onSave: () => void;
  onCancel: () => void;
}

interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: string;
  customizable: boolean;
  slug: string;
  image: string;
  modelUrl: string;
}

export default function ProductForm({
  product,
  onSave,
  onCancel,
}: ProductFormProps) {
  const { colors } = useTheme();
  const isEditing = !!product;

  const [formData, setFormData] = useState<ProductFormData>({
    name: product?.name || "",
    description: product?.description || "",
    price: product?.price?.toString() || "",
    category: product?.category || "",
    customizable: product?.customizable || false,
    slug: product?.slug || "",
    image: product?.image || "",
    modelUrl: product?.modelUrl || "",
  });

  const [loading, setLoading] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);
  const [selectedImageUri, setSelectedImageUri] = useState<string | null>(null);

  useEffect(() => {
    // Auto-generate slug from name
    if (formData.name && !isEditing) {
      const slug = formData.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/(^-|-$)/g, "");
      setFormData((prev) => ({ ...prev, slug }));
    }
  }, [formData.name, isEditing]);

  const handleInputChange = (
    field: keyof ProductFormData,
    value: string | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleImagePicker = async () => {
    try {
      // Request permission
      const permissionResult =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permissionResult.granted === false) {
        Alert.alert(
          "Permission Required",
          "Permission to access camera roll is required!"
        );
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImageUri(result.assets[0].uri);
        await uploadProductImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Image picker error:", error);
      Alert.alert("Error", "Failed to pick image");
    }
  };

  const uploadProductImage = async (imageUri: string) => {
    try {
      setImageUploading(true);

      const uploadResult = await uploadImageFromUri(imageUri, {
        fileName: `product_${Date.now()}.jpg`,
        folder: "/products",
        tags: ["product", "mobile-upload"],
      });

      setFormData((prev) => ({
        ...prev,
        image: uploadResult.url,
      }));

      Alert.alert("Success", "Image uploaded successfully!");
    } catch (error) {
      console.error("Image upload error:", error);
      Alert.alert("Upload Failed", "Failed to upload image. Please try again.");
    } finally {
      setImageUploading(false);
    }
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      Alert.alert("Validation Error", "Product name is required");
      return false;
    }
    if (!formData.description.trim()) {
      Alert.alert("Validation Error", "Product description is required");
      return false;
    }
    if (!formData.price.trim() || isNaN(Number(formData.price))) {
      Alert.alert("Validation Error", "Valid price is required");
      return false;
    }
    if (!formData.category.trim()) {
      Alert.alert("Validation Error", "Product category is required");
      return false;
    }
    if (!formData.slug.trim()) {
      Alert.alert("Validation Error", "Product slug is required");
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const productData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: Number(formData.price),
        category: formData.category.trim(),
        customizable: formData.customizable,
        slug: formData.slug.trim(),
        image: formData.image,
        modelUrl: formData.modelUrl.trim() || undefined,
      };

      if (isEditing) {
        // Update existing product
        console.log("Update product:", product!.id, productData);
        // await apiClient.updateProduct(product!.id, productData);
        Alert.alert("Success", "Product updated successfully!");
      } else {
        // Create new product
        console.log("Create product:", productData);
        // await apiClient.createProduct(productData);
        Alert.alert("Success", "Product created successfully!");
      }

      onSave();
    } catch (error) {
      console.error("Save product error:", error);
      Alert.alert("Error", "Failed to save product. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onCancel} style={styles.cancelButton}>
          <Text
            style={[styles.cancelButtonText, { color: colors.text.secondary }]}
          >
            Cancel
          </Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          {isEditing ? "Edit Product" : "Add Product"}
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Product Image */}
        <Card variant="elevated" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Product Image
          </Text>

          <TouchableOpacity
            style={[
              styles.imageUploadArea,
              { backgroundColor: colors.background.secondary },
            ]}
            onPress={handleImagePicker}
            disabled={imageUploading}
          >
            {selectedImageUri || formData.image ? (
              <Image
                source={{ uri: selectedImageUri || formData.image }}
                style={styles.uploadedImage}
                resizeMode="cover"
              />
            ) : (
              <View style={styles.imagePlaceholder}>
                <Text
                  style={[
                    styles.imagePlaceholderIcon,
                    { color: colors.text.tertiary },
                  ]}
                >
                  📷
                </Text>
                <Text
                  style={[
                    styles.imagePlaceholderText,
                    { color: colors.text.secondary },
                  ]}
                >
                  Tap to upload image
                </Text>
              </View>
            )}

            {imageUploading && (
              <View style={styles.uploadingOverlay}>
                <Text style={[styles.uploadingText, { color: colors.white }]}>
                  Uploading...
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </Card>

        {/* Basic Information */}
        <Card variant="elevated" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Basic Information
          </Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
              Product Name *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: colors.background.secondary,
                  color: colors.text.primary,
                  borderColor: colors.border.light,
                },
              ]}
              placeholder="Enter product name"
              placeholderTextColor={colors.text.tertiary}
              value={formData.name}
              onChangeText={(value) => handleInputChange("name", value)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
              Description *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                styles.textArea,
                {
                  backgroundColor: colors.background.secondary,
                  color: colors.text.primary,
                  borderColor: colors.border.light,
                },
              ]}
              placeholder="Enter product description"
              placeholderTextColor={colors.text.tertiary}
              value={formData.description}
              onChangeText={(value) => handleInputChange("description", value)}
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.inputRow}>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
                Price *
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    backgroundColor: colors.background.secondary,
                    color: colors.text.primary,
                    borderColor: colors.border.light,
                  },
                ]}
                placeholder="0.00"
                placeholderTextColor={colors.text.tertiary}
                value={formData.price}
                onChangeText={(value) => handleInputChange("price", value)}
                keyboardType="numeric"
              />
            </View>

            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
                Category *
              </Text>
              <TextInput
                style={[
                  styles.textInput,
                  {
                    backgroundColor: colors.background.secondary,
                    color: colors.text.primary,
                    borderColor: colors.border.light,
                  },
                ]}
                placeholder="Enter category"
                placeholderTextColor={colors.text.tertiary}
                value={formData.category}
                onChangeText={(value) => handleInputChange("category", value)}
              />
            </View>
          </View>
        </Card>

        {/* Advanced Settings */}
        <Card variant="elevated" padding="lg" style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
            Advanced Settings
          </Text>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
              Product Slug *
            </Text>
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: colors.background.secondary,
                  color: colors.text.primary,
                  borderColor: colors.border.light,
                },
              ]}
              placeholder="product-slug"
              placeholderTextColor={colors.text.tertiary}
              value={formData.slug}
              onChangeText={(value) => handleInputChange("slug", value)}
            />
            <Text style={[styles.inputHint, { color: colors.text.tertiary }]}>
              URL-friendly version of the product name
            </Text>
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text.primary }]}>
              3D Model URL (Optional)
            </Text>
            <TextInput
              style={[
                styles.textInput,
                {
                  backgroundColor: colors.background.secondary,
                  color: colors.text.primary,
                  borderColor: colors.border.light,
                },
              ]}
              placeholder="https://example.com/model.glb"
              placeholderTextColor={colors.text.tertiary}
              value={formData.modelUrl}
              onChangeText={(value) => handleInputChange("modelUrl", value)}
            />
            <Text style={[styles.inputHint, { color: colors.text.tertiary }]}>
              URL to 3D model file for AR/VR viewing
            </Text>
          </View>

          <View style={styles.switchGroup}>
            <View style={styles.switchInfo}>
              <Text
                style={[styles.switchLabel, { color: colors.text.primary }]}
              >
                Customizable Product
              </Text>
              <Text
                style={[
                  styles.switchDescription,
                  { color: colors.text.secondary },
                ]}
              >
                Allow customers to customize this product
              </Text>
            </View>
            <Switch
              value={formData.customizable}
              onValueChange={(value) =>
                handleInputChange("customizable", value)
              }
              trackColor={{
                false: colors.border.light,
                true: colors.primary[300],
              }}
              thumbColor={
                formData.customizable
                  ? colors.primary[500]
                  : colors.text.tertiary
              }
            />
          </View>
        </Card>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Save Button */}
      <View
        style={[styles.footer, { backgroundColor: colors.background.primary }]}
      >
        <Button
          title={
            loading
              ? "Saving..."
              : isEditing
              ? "Update Product"
              : "Create Product"
          }
          onPress={handleSave}
          variant="primary"
          size="lg"
          fullWidth
          loading={loading}
          disabled={loading || imageUploading}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  cancelButton: {
    padding: Spacing.sm,
  },
  cancelButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
  },
  placeholder: {
    width: 60,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  section: {
    marginTop: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.md,
  },
  imageUploadArea: {
    height: 200,
    borderRadius: BorderRadius.lg,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: Colors.border.light,
    borderStyle: "dashed",
    position: "relative",
    overflow: "hidden",
  },
  uploadedImage: {
    width: "100%",
    height: "100%",
    borderRadius: BorderRadius.lg,
  },
  imagePlaceholder: {
    alignItems: "center",
  },
  imagePlaceholderIcon: {
    fontSize: 48,
    marginBottom: Spacing.sm,
  },
  imagePlaceholderText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  uploadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.7)",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: BorderRadius.lg,
  },
  uploadingText: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  inputRow: {
    flexDirection: "row",
    gap: Spacing.md,
  },
  halfWidth: {
    flex: 1,
  },
  inputLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.sm,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.md,
    fontSize: Typography.fontSize.base,
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: "top",
  },
  inputHint: {
    fontSize: Typography.fontSize.sm,
    marginTop: Spacing.xs,
  },
  switchGroup: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: Spacing.sm,
  },
  switchInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },
  switchLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  switchDescription: {
    fontSize: Typography.fontSize.sm,
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border.light,
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
});
