/**
 * Six Light Media Store - Configuration Debug Component
 * Debug component to verify environment variables and configuration
 */

import Constants from "expo-constants";
import React from "react";
import { Alert, ScrollView, StyleSheet, Text, View } from "react-native";

import Card from "@/components/ui/Card";
import { Spacing, Typography } from "@/constants/Theme";
import { useTheme } from "@/contexts/ThemeContext";
import EnvironmentSwitcher from "./EnvironmentSwitcher";

export default function ConfigDebug() {
  const { colors } = useTheme();
  const [apiStatus, setApiStatus] = React.useState<
    "checking" | "online" | "offline"
  >("checking");
  const [lastCheck, setLastCheck] = React.useState<Date | null>(null);

  React.useEffect(() => {
    checkApiStatus();
  }, []);

  const checkApiStatus = async () => {
    try {
      setApiStatus("checking");
      // Import apiClient dynamically to avoid import issues
      const { apiClient } = await import("@/services/api");
      await apiClient.healthCheck();
      setApiStatus("online");
    } catch (error) {
      console.log("API Health check failed:", error);
      setApiStatus("offline");
    } finally {
      setLastCheck(new Date());
    }
  };

  const testApiConnection = async () => {
    try {
      const { apiClient } = await import("@/services/api");
      const result = await apiClient.healthCheck();
      Alert.alert(
        "API Test",
        `Success! Server responded: ${JSON.stringify(result)}`
      );
    } catch (error) {
      Alert.alert(
        "API Test",
        `Failed: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  };

  const getStatusColor = () => {
    switch (apiStatus) {
      case "online":
        return colors.success?.[500] || "#10B981";
      case "offline":
        return colors.error || "#EF4444";
      default:
        return colors.warning?.[500] || "#F59E0B";
    }
  };

  const getStatusText = () => {
    switch (apiStatus) {
      case "online":
        return "✅ Online";
      case "offline":
        return "❌ Offline";
      default:
        return "⏳ Checking...";
    }
  };

  const config = {
    // Environment Variables
    env: {
      EXPO_PUBLIC_API_URL: process.env.EXPO_PUBLIC_API_URL,
      EXPO_PUBLIC_IMAGEKIT_URL_ENDPOINT:
        process.env.EXPO_PUBLIC_IMAGEKIT_URL_ENDPOINT,
      EXPO_PUBLIC_IMAGEKIT_PUBLIC_KEY:
        process.env.EXPO_PUBLIC_IMAGEKIT_PUBLIC_KEY,
      EXPO_PUBLIC_IMAGEKIT_PRIVATE_KEY:
        process.env.EXPO_PUBLIC_IMAGEKIT_PRIVATE_KEY,
      EXPO_PUBLIC_SITE_URL: process.env.EXPO_PUBLIC_SITE_URL,
      NODE_ENV: process.env.NODE_ENV,
    },
    // Expo Config Extra
    extra: Constants.expoConfig?.extra || {},
    // App Info
    app: {
      name: Constants.expoConfig?.name,
      slug: Constants.expoConfig?.slug,
      version: Constants.expoConfig?.version,
    },
  };

  const renderConfigSection = (title: string, data: any) => (
    <Card variant="elevated" padding="lg" style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
        {title}
      </Text>
      {Object.entries(data).map(([key, value]) => (
        <View key={key} style={styles.configItem}>
          <Text style={[styles.configKey, { color: colors.text.secondary }]}>
            {key}:
          </Text>
          <Text style={[styles.configValue, { color: colors.text.primary }]}>
            {typeof value === "string"
              ? value.length > 50
                ? `${value.substring(0, 50)}...`
                : value
              : JSON.stringify(value)}
          </Text>
        </View>
      ))}
    </Card>
  );

  // Only show in development mode
  if (!__DEV__) {
    return null;
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: colors.text.primary }]}>
          🔧 Configuration Debug
        </Text>
        <Text style={[styles.headerSubtitle, { color: colors.text.secondary }]}>
          Development mode only - Environment variables and configuration
        </Text>
      </View>

      {/* Environment Switcher */}
      <EnvironmentSwitcher />

      {/* API Status Section */}
      <Card variant="elevated" padding="lg" style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          🌐 API Status
        </Text>
        <View style={styles.statusItem}>
          <Text style={[styles.statusLabel, { color: colors.text.secondary }]}>
            Connection Status:
          </Text>
          <Text style={[styles.statusValue, { color: getStatusColor() }]}>
            {getStatusText()}
          </Text>
        </View>
        {lastCheck && (
          <View style={styles.statusItem}>
            <Text
              style={[styles.statusLabel, { color: colors.text.secondary }]}
            >
              Last checked:
            </Text>
            <Text style={[styles.statusValue, { color: colors.text.primary }]}>
              {lastCheck.toLocaleTimeString()}
            </Text>
          </View>
        )}
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[styles.button, { backgroundColor: colors.primary[500] }]}
            onPress={checkApiStatus}
          >
            <Text style={styles.buttonText}>Refresh Status</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, { backgroundColor: colors.secondary[500] }]}
            onPress={testApiConnection}
          >
            <Text style={styles.buttonText}>Test Connection</Text>
          </TouchableOpacity>
        </View>
      </Card>

      {renderConfigSection("Environment Variables", config.env)}
      {renderConfigSection("Expo Config Extra", config.extra)}
      {renderConfigSection("App Information", config.app)}

      {/* API Test Section */}
      <Card variant="elevated" padding="lg" style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          🌐 API Configuration
        </Text>
        <View style={styles.configItem}>
          <Text style={[styles.configKey, { color: colors.text.secondary }]}>
            Final API URL:
          </Text>
          <Text style={[styles.configValue, { color: colors.primary[500] }]}>
            {process.env.EXPO_PUBLIC_API_URL ||
              Constants.expoConfig?.extra?.apiUrl ||
              "https://backendapi-sixlight.onrender.com"}
          </Text>
        </View>
        <View style={styles.configItem}>
          <Text style={[styles.configKey, { color: colors.text.secondary }]}>
            ImageKit Endpoint:
          </Text>
          <Text style={[styles.configValue, { color: colors.primary[500] }]}>
            {process.env.EXPO_PUBLIC_IMAGEKIT_URL_ENDPOINT ||
              Constants.expoConfig?.extra?.imagekitUrlEndpoint ||
              "https://ik.imagekit.io/fwbvmq9re"}
          </Text>
        </View>
      </Card>

      {/* Status Indicators */}
      <Card variant="elevated" padding="lg" style={styles.section}>
        <Text style={[styles.sectionTitle, { color: colors.text.primary }]}>
          ✅ Status Check
        </Text>
        <View style={styles.statusItem}>
          <Text style={[styles.statusLabel, { color: colors.text.secondary }]}>
            API URL Configured:
          </Text>
          <Text
            style={[
              styles.statusValue,
              {
                color: process.env.EXPO_PUBLIC_API_URL
                  ? colors.success[500]
                  : colors.error,
              },
            ]}
          >
            {process.env.EXPO_PUBLIC_API_URL
              ? "✅ Yes"
              : "❌ No (using fallback)"}
          </Text>
        </View>
        <View style={styles.statusItem}>
          <Text style={[styles.statusLabel, { color: colors.text.secondary }]}>
            ImageKit Configured:
          </Text>
          <Text
            style={[
              styles.statusValue,
              {
                color: process.env.EXPO_PUBLIC_IMAGEKIT_URL_ENDPOINT
                  ? colors.success[500]
                  : colors.error,
              },
            ]}
          >
            {process.env.EXPO_PUBLIC_IMAGEKIT_URL_ENDPOINT
              ? "✅ Yes"
              : "❌ No (using fallback)"}
          </Text>
        </View>
        <View style={styles.statusItem}>
          <Text style={[styles.statusLabel, { color: colors.text.secondary }]}>
            Production Backend:
          </Text>
          <Text
            style={[
              styles.statusValue,
              {
                color: (process.env.EXPO_PUBLIC_API_URL || "").includes(
                  "onrender.com"
                )
                  ? colors.success[500]
                  : colors.warning[500],
              },
            ]}
          >
            {(process.env.EXPO_PUBLIC_API_URL || "").includes("onrender.com")
              ? "✅ Connected"
              : "⚠️ Local/Fallback"}
          </Text>
        </View>
      </Card>

      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: Spacing.lg,
  },
  header: {
    paddingVertical: Spacing.lg,
  },
  headerTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: "bold",
    marginBottom: Spacing.sm,
  },
  headerSubtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: 24,
  },
  section: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: "bold",
    marginBottom: Spacing.md,
  },
  configItem: {
    marginBottom: Spacing.sm,
  },
  configKey: {
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
    marginBottom: Spacing.xs,
  },
  configValue: {
    fontSize: Typography.fontSize.sm,
    fontFamily: "monospace",
    paddingLeft: Spacing.md,
  },
  statusItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Spacing.sm,
  },
  statusLabel: {
    fontSize: Typography.fontSize.base,
    fontWeight: "500",
  },
  statusValue: {
    fontSize: Typography.fontSize.base,
    fontWeight: "600",
  },
  bottomSpacing: {
    height: Spacing["3xl"],
  },
  buttonRow: {
    flexDirection: "row",
    gap: Spacing.md,
    marginTop: Spacing.md,
  },
  button: {
    flex: 1,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: 8,
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontSize: Typography.fontSize.sm,
    fontWeight: "600",
  },
});
