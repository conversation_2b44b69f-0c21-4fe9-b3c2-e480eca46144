/**
 * Six Light Media Store - Environment Switcher
 * Development tool to switch between local and production backends
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { useTheme } from '@/contexts/ThemeContext';
import { Colors, Spacing, BorderRadius, Typography } from '@/constants/Theme';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

const ENVIRONMENT_KEY = '@sixlight_environment';

interface EnvironmentConfig {
  name: string;
  apiUrl: string;
  siteUrl: string;
  description: string;
  color: string;
}

const ENVIRONMENTS: EnvironmentConfig[] = [
  {
    name: 'Development',
    apiUrl: 'http://localhost:3001',
    siteUrl: 'http://localhost:3000',
    description: 'Local development server',
    color: '#10B981', // green
  },
  {
    name: 'Production',
    apiUrl: 'https://backendapi-sixlight.onrender.com',
    siteUrl: 'https://sixlightmediastorebeta.netlify.app',
    description: 'Live production server',
    color: '#EF4444', // red
  },
];

export default function EnvironmentSwitcher() {
  const { colors } = useTheme();
  const [currentEnvironment, setCurrentEnvironment] = useState<string>('Development');
  const [loading, setLoading] = useState(false);

  React.useEffect(() => {
    loadCurrentEnvironment();
  }, []);

  const loadCurrentEnvironment = async () => {
    try {
      const saved = await AsyncStorage.getItem(ENVIRONMENT_KEY);
      if (saved) {
        setCurrentEnvironment(saved);
      }
    } catch (error) {
      console.error('Failed to load environment setting:', error);
    }
  };

  const switchEnvironment = async (envName: string) => {
    try {
      setLoading(true);
      await AsyncStorage.setItem(ENVIRONMENT_KEY, envName);
      setCurrentEnvironment(envName);
      
      Alert.alert(
        'Environment Switched',
        `Switched to ${envName} environment. Please restart the app for changes to take effect.`,
        [
          {
            text: 'OK',
            onPress: () => {
              // In a real app, you might want to trigger a reload here
              console.log(`Environment switched to: ${envName}`);
            },
          },
        ]
      );
    } catch (error) {
      console.error('Failed to save environment setting:', error);
      Alert.alert('Error', 'Failed to switch environment');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentConfig = () => {
    return ENVIRONMENTS.find(env => env.name === currentEnvironment) || ENVIRONMENTS[0];
  };

  const currentConfig = getCurrentConfig();

  // Only show in development mode
  if (!__DEV__) {
    return null;
  }

  return (
    <Card variant="elevated" padding="lg" style={styles.container}>
      <Text style={[styles.title, { color: colors.text.primary }]}>
        🔄 Environment Switcher
      </Text>
      <Text style={[styles.subtitle, { color: colors.text.secondary }]}>
        Switch between local development and production backends
      </Text>

      {/* Current Environment */}
      <View style={styles.currentEnvironment}>
        <Text style={[styles.currentLabel, { color: colors.text.secondary }]}>
          Current Environment:
        </Text>
        <View style={[styles.currentBadge, { backgroundColor: currentConfig.color + '20' }]}>
          <View style={[styles.statusDot, { backgroundColor: currentConfig.color }]} />
          <Text style={[styles.currentName, { color: currentConfig.color }]}>
            {currentConfig.name}
          </Text>
        </View>
      </View>

      {/* Current Configuration */}
      <View style={styles.configSection}>
        <Text style={[styles.configLabel, { color: colors.text.secondary }]}>
          API URL:
        </Text>
        <Text style={[styles.configValue, { color: colors.text.primary }]}>
          {currentConfig.apiUrl}
        </Text>
        <Text style={[styles.configLabel, { color: colors.text.secondary }]}>
          Site URL:
        </Text>
        <Text style={[styles.configValue, { color: colors.text.primary }]}>
          {currentConfig.siteUrl}
        </Text>
      </View>

      {/* Environment Options */}
      <View style={styles.environmentOptions}>
        <Text style={[styles.optionsTitle, { color: colors.text.primary }]}>
          Switch Environment:
        </Text>
        {ENVIRONMENTS.map((env) => (
          <TouchableOpacity
            key={env.name}
            style={[
              styles.environmentOption,
              {
                backgroundColor: currentEnvironment === env.name 
                  ? env.color + '20' 
                  : colors.background.secondary,
                borderColor: currentEnvironment === env.name 
                  ? env.color 
                  : colors.border.light,
              },
            ]}
            onPress={() => switchEnvironment(env.name)}
            disabled={loading || currentEnvironment === env.name}
          >
            <View style={styles.environmentHeader}>
              <View style={[styles.environmentDot, { backgroundColor: env.color }]} />
              <Text style={[
                styles.environmentName, 
                { 
                  color: currentEnvironment === env.name 
                    ? env.color 
                    : colors.text.primary 
                }
              ]}>
                {env.name}
              </Text>
              {currentEnvironment === env.name && (
                <Text style={[styles.currentIndicator, { color: env.color }]}>
                  ✓
                </Text>
              )}
            </View>
            <Text style={[styles.environmentDescription, { color: colors.text.secondary }]}>
              {env.description}
            </Text>
            <Text style={[styles.environmentUrl, { color: colors.text.tertiary }]}>
              {env.apiUrl}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Warning */}
      <View style={[styles.warning, { backgroundColor: colors.warning[100] }]}>
        <Text style={[styles.warningText, { color: colors.warning[700] }]}>
          ⚠️ Restart the app after switching environments for changes to take effect.
        </Text>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.lg,
  },
  title: {
    fontSize: Typography.fontSize.lg,
    fontWeight: 'bold',
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Spacing.lg,
    lineHeight: 20,
  },
  currentEnvironment: {
    marginBottom: Spacing.lg,
  },
  currentLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '500',
    marginBottom: Spacing.sm,
  },
  currentBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: Spacing.sm,
  },
  currentName: {
    fontSize: Typography.fontSize.base,
    fontWeight: '600',
  },
  configSection: {
    marginBottom: Spacing.lg,
  },
  configLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '500',
    marginBottom: Spacing.xs,
    marginTop: Spacing.sm,
  },
  configValue: {
    fontSize: Typography.fontSize.sm,
    fontFamily: 'monospace',
    paddingLeft: Spacing.md,
  },
  environmentOptions: {
    marginBottom: Spacing.lg,
  },
  optionsTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: '600',
    marginBottom: Spacing.md,
  },
  environmentOption: {
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    marginBottom: Spacing.sm,
  },
  environmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  environmentDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: Spacing.sm,
  },
  environmentName: {
    fontSize: Typography.fontSize.base,
    fontWeight: '600',
    flex: 1,
  },
  currentIndicator: {
    fontSize: Typography.fontSize.lg,
    fontWeight: 'bold',
  },
  environmentDescription: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Spacing.xs,
  },
  environmentUrl: {
    fontSize: Typography.fontSize.xs,
    fontFamily: 'monospace',
  },
  warning: {
    padding: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  warningText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: '500',
    textAlign: 'center',
  },
});
