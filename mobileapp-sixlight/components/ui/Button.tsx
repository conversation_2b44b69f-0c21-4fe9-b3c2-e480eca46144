/**
 * Six Light Media Store - Button Component
 * Extraordinary button component with multiple variants and animations
 */

import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  View,
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { Colors, BorderRadius, Spacing } from '@/constants/Theme';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export default function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  icon,
  iconPosition = 'left',
  style,
  textStyle,
}: ButtonProps) {
  const { colors, isDark } = useTheme();

  const getButtonStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      borderRadius: BorderRadius.lg,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
    };

    // Size styles
    const sizeStyles: Record<string, ViewStyle> = {
      sm: {
        paddingHorizontal: Spacing.md,
        paddingVertical: Spacing.sm,
        minHeight: 36,
      },
      md: {
        paddingHorizontal: Spacing.lg,
        paddingVertical: Spacing.md,
        minHeight: 44,
      },
      lg: {
        paddingHorizontal: Spacing.xl,
        paddingVertical: Spacing.lg,
        minHeight: 52,
      },
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      primary: {
        backgroundColor: disabled ? colors.secondary[300] : colors.primary[500],
        borderColor: disabled ? colors.secondary[300] : colors.primary[500],
      },
      secondary: {
        backgroundColor: disabled ? colors.secondary[100] : colors.secondary[200],
        borderColor: disabled ? colors.secondary[100] : colors.secondary[200],
      },
      outline: {
        backgroundColor: colors.transparent,
        borderColor: disabled ? colors.secondary[300] : colors.primary[500],
      },
      ghost: {
        backgroundColor: colors.transparent,
        borderColor: colors.transparent,
      },
      danger: {
        backgroundColor: disabled ? colors.secondary[300] : colors.error,
        borderColor: disabled ? colors.secondary[300] : colors.error,
      },
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      width: fullWidth ? '100%' : 'auto',
      opacity: disabled ? 0.6 : 1,
    };
  };

  const getTextStyles = (): TextStyle => {
    const baseFontSize = {
      sm: 14,
      md: 16,
      lg: 18,
    };

    const variantTextStyles: Record<string, TextStyle> = {
      primary: {
        color: disabled ? colors.secondary[500] : colors.white,
      },
      secondary: {
        color: disabled ? colors.secondary[500] : colors.secondary[700],
      },
      outline: {
        color: disabled ? colors.secondary[400] : colors.primary[500],
      },
      ghost: {
        color: disabled ? colors.secondary[400] : colors.primary[500],
      },
      danger: {
        color: disabled ? colors.secondary[500] : colors.white,
      },
    };

    return {
      fontSize: baseFontSize[size],
      fontWeight: '600',
      textAlign: 'center',
      ...variantTextStyles[variant],
    };
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="small"
            color={variant === 'primary' || variant === 'danger' ? colors.white : colors.primary[500]}
          />
          <Text style={[getTextStyles(), { marginLeft: Spacing.sm }, textStyle]}>
            {title}
          </Text>
        </View>
      );
    }

    if (icon) {
      return (
        <View style={styles.contentContainer}>
          {iconPosition === 'left' && (
            <View style={[styles.iconContainer, { marginRight: Spacing.sm }]}>
              {icon}
            </View>
          )}
          <Text style={[getTextStyles(), textStyle]}>{title}</Text>
          {iconPosition === 'right' && (
            <View style={[styles.iconContainer, { marginLeft: Spacing.sm }]}>
              {icon}
            </View>
          )}
        </View>
      );
    }

    return <Text style={[getTextStyles(), textStyle]}>{title}</Text>;
  };

  return (
    <TouchableOpacity
      style={[getButtonStyles(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {renderContent()}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
