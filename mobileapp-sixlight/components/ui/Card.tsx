/**
 * Six Light Media Store - Card Component
 * Elegant card component with shadow and customization options
 */

import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { BorderRadius, Spacing, Shadows } from '@/constants/Theme';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'flat';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  margin?: 'none' | 'sm' | 'md' | 'lg';
  onPress?: () => void;
  style?: ViewStyle;
  disabled?: boolean;
}

export default function Card({
  children,
  variant = 'default',
  padding = 'md',
  margin = 'none',
  onPress,
  style,
  disabled = false,
}: CardProps) {
  const { colors, isDark } = useTheme();

  const getCardStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      borderRadius: BorderRadius.lg,
      backgroundColor: colors.background.primary,
    };

    // Padding styles
    const paddingStyles: Record<string, ViewStyle> = {
      none: {},
      sm: { padding: Spacing.sm },
      md: { padding: Spacing.md },
      lg: { padding: Spacing.lg },
    };

    // Margin styles
    const marginStyles: Record<string, ViewStyle> = {
      none: {},
      sm: { margin: Spacing.sm },
      md: { margin: Spacing.md },
      lg: { margin: Spacing.lg },
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      default: {
        ...Shadows.md,
        backgroundColor: colors.background.primary,
      },
      elevated: {
        ...Shadows.lg,
        backgroundColor: colors.background.primary,
      },
      outlined: {
        borderWidth: 1,
        borderColor: colors.border.light,
        backgroundColor: colors.background.primary,
      },
      flat: {
        backgroundColor: colors.background.secondary,
      },
    };

    return {
      ...baseStyles,
      ...paddingStyles[padding],
      ...marginStyles[margin],
      ...variantStyles[variant],
      opacity: disabled ? 0.6 : 1,
    };
  };

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent
      style={[getCardStyles(), style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={onPress ? 0.95 : 1}
    >
      {children}
    </CardComponent>
  );
}

// Product Card Component
interface ProductCardProps {
  product: {
    id: number;
    name: string;
    price: number;
    image: string;
    inStock: boolean;
  };
  onPress: () => void;
  onAddToCart?: () => void;
  onToggleWishlist?: () => void;
  isInWishlist?: boolean;
  style?: ViewStyle;
}

export function ProductCard({
  product,
  onPress,
  onAddToCart,
  onToggleWishlist,
  isInWishlist = false,
  style,
}: ProductCardProps) {
  const { colors } = useTheme();

  return (
    <Card variant="elevated" padding="none" onPress={onPress} style={[styles.productCard, style]}>
      <View style={styles.productImageContainer}>
        {/* Product Image */}
        <View style={[styles.productImage, { backgroundColor: colors.background.secondary }]}>
          {/* Image placeholder - will be replaced with actual image */}
          <View style={styles.imagePlaceholder} />
        </View>
        
        {/* Wishlist Button */}
        {onToggleWishlist && (
          <TouchableOpacity
            style={[styles.wishlistButton, { backgroundColor: colors.background.primary }]}
            onPress={onToggleWishlist}
          >
            <View style={[styles.heartIcon, { backgroundColor: isInWishlist ? colors.error : colors.secondary[400] }]} />
          </TouchableOpacity>
        )}
        
        {/* Stock Badge */}
        {!product.inStock && (
          <View style={[styles.stockBadge, { backgroundColor: colors.error }]}>
            <View style={styles.stockBadgeText} />
          </View>
        )}
      </View>
      
      <View style={styles.productInfo}>
        {/* Product Name */}
        <View style={[styles.productName, { backgroundColor: colors.text.primary }]} />
        
        {/* Product Price */}
        <View style={styles.priceContainer}>
          <View style={[styles.price, { backgroundColor: colors.primary[500] }]} />
        </View>
        
        {/* Add to Cart Button */}
        {onAddToCart && product.inStock && (
          <TouchableOpacity
            style={[styles.addToCartButton, { backgroundColor: colors.primary[500] }]}
            onPress={onAddToCart}
          >
            <View style={styles.addToCartIcon} />
          </TouchableOpacity>
        )}
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  productCard: {
    width: 180,
    marginBottom: Spacing.md,
  },
  productImageContainer: {
    position: 'relative',
    height: 140,
    borderTopLeftRadius: BorderRadius.lg,
    borderTopRightRadius: BorderRadius.lg,
    overflow: 'hidden',
  },
  productImage: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePlaceholder: {
    width: 60,
    height: 60,
    backgroundColor: '#E5E7EB',
    borderRadius: BorderRadius.md,
  },
  wishlistButton: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.sm,
  },
  heartIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  stockBadge: {
    position: 'absolute',
    top: Spacing.sm,
    left: Spacing.sm,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
    borderRadius: BorderRadius.sm,
  },
  stockBadgeText: {
    width: 40,
    height: 12,
    backgroundColor: 'white',
    borderRadius: 2,
  },
  productInfo: {
    padding: Spacing.md,
  },
  productName: {
    height: 16,
    borderRadius: 2,
    marginBottom: Spacing.sm,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: Spacing.sm,
  },
  price: {
    width: 60,
    height: 20,
    borderRadius: BorderRadius.sm,
  },
  addToCartButton: {
    height: 36,
    borderRadius: BorderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addToCartIcon: {
    width: 20,
    height: 20,
    backgroundColor: 'white',
    borderRadius: 2,
  },
});
