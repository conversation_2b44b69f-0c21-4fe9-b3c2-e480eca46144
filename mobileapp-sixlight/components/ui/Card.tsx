/**
 * Six Light Media Store - Card Component
 * Elegant card component with shadow and customization options
 */

import React from "react";
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  Text,
  Image,
} from "react-native";
import { useTheme } from "@/contexts/ThemeContext";
import { BorderRadius, Spacing, Shadows } from "@/constants/Theme";

interface CardProps {
  children: React.ReactNode;
  variant?: "default" | "elevated" | "outlined" | "flat";
  padding?: "none" | "sm" | "md" | "lg";
  margin?: "none" | "sm" | "md" | "lg";
  onPress?: () => void;
  style?: ViewStyle;
  disabled?: boolean;
}

export default function Card({
  children,
  variant = "default",
  padding = "md",
  margin = "none",
  onPress,
  style,
  disabled = false,
}: CardProps) {
  const { colors, isDark } = useTheme();

  const getCardStyles = (): ViewStyle => {
    const baseStyles: ViewStyle = {
      borderRadius: BorderRadius.lg,
      backgroundColor: colors.background.primary,
    };

    // Padding styles
    const paddingStyles: Record<string, ViewStyle> = {
      none: {},
      sm: { padding: Spacing.sm },
      md: { padding: Spacing.md },
      lg: { padding: Spacing.lg },
    };

    // Margin styles
    const marginStyles: Record<string, ViewStyle> = {
      none: {},
      sm: { margin: Spacing.sm },
      md: { margin: Spacing.md },
      lg: { margin: Spacing.lg },
    };

    // Variant styles
    const variantStyles: Record<string, ViewStyle> = {
      default: {
        ...Shadows.md,
        backgroundColor: colors.background.primary,
      },
      elevated: {
        ...Shadows.lg,
        backgroundColor: colors.background.primary,
      },
      outlined: {
        borderWidth: 1,
        borderColor: colors.border.light,
        backgroundColor: colors.background.primary,
      },
      flat: {
        backgroundColor: colors.background.secondary,
      },
    };

    return {
      ...baseStyles,
      ...paddingStyles[padding],
      ...marginStyles[margin],
      ...variantStyles[variant],
      opacity: disabled ? 0.6 : 1,
    };
  };

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent
      style={[getCardStyles(), style]}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={onPress ? 0.95 : 1}
    >
      {children}
    </CardComponent>
  );
}

// Product Card Component
interface ProductCardProps {
  product: {
    id: number;
    name: string;
    price: number;
    image: string;
    customizable?: boolean;
  };
  onPress: () => void;
  onAddToCart?: () => void;
  onToggleWishlist?: () => void;
  isInWishlist?: boolean;
  style?: ViewStyle;
}

export function ProductCard({
  product,
  onPress,
  onAddToCart,
  onToggleWishlist,
  isInWishlist = false,
  style,
}: ProductCardProps) {
  const { colors } = useTheme();

  return (
    <Card
      variant="elevated"
      padding="none"
      onPress={onPress}
      style={[styles.productCard, style]}
    >
      <View style={styles.productImageContainer}>
        {/* Product Image */}
        <View
          style={[
            styles.productImage,
            { backgroundColor: colors.background.secondary },
          ]}
        >
          {product.image ? (
            <Image
              source={{ uri: product.image }}
              style={styles.productImageActual}
              resizeMode="cover"
            />
          ) : (
            <View
              style={[
                styles.imagePlaceholder,
                { backgroundColor: colors.secondary[200] },
              ]}
            >
              <Text
                style={[
                  styles.placeholderText,
                  { color: colors.secondary[500] },
                ]}
              >
                No Image
              </Text>
            </View>
          )}
        </View>

        {/* Wishlist Button */}
        {onToggleWishlist && (
          <TouchableOpacity
            style={[
              styles.wishlistButton,
              { backgroundColor: colors.background.primary },
            ]}
            onPress={onToggleWishlist}
          >
            <Text
              style={[
                styles.heartIcon,
                {
                  color: isInWishlist
                    ? colors.primary[500]
                    : colors.secondary[400],
                },
              ]}
            >
              ♥
            </Text>
          </TouchableOpacity>
        )}

        {/* Customizable Badge */}
        {product.customizable && (
          <View
            style={[
              styles.customizableBadge,
              { backgroundColor: colors.primary[500] },
            ]}
          >
            <Text
              style={[styles.customizableBadgeText, { color: colors.white }]}
            >
              Custom
            </Text>
          </View>
        )}
      </View>

      <View style={styles.productInfo}>
        {/* Product Name */}
        <Text
          style={[styles.productName, { color: colors.text.primary }]}
          numberOfLines={2}
        >
          {product.name}
        </Text>

        {/* Product Price */}
        <View style={styles.priceContainer}>
          <Text style={[styles.price, { color: colors.primary[500] }]}>
            ${product.price.toFixed(2)}
          </Text>
        </View>

        {/* Add to Cart Button */}
        {onAddToCart && (
          <TouchableOpacity
            style={[
              styles.addToCartButton,
              { backgroundColor: colors.primary[500] },
            ]}
            onPress={onAddToCart}
          >
            <Text style={[styles.addToCartText, { color: colors.white }]}>
              Add to Cart
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  productCard: {
    width: 180,
    marginBottom: Spacing.md,
  },
  productImageContainer: {
    position: "relative",
    height: 140,
    borderTopLeftRadius: BorderRadius.lg,
    borderTopRightRadius: BorderRadius.lg,
    overflow: "hidden",
  },
  productImage: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  productImageActual: {
    width: "100%",
    height: "100%",
  },
  imagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: BorderRadius.md,
    justifyContent: "center",
    alignItems: "center",
  },
  placeholderText: {
    fontSize: 12,
    fontWeight: "500",
  },
  wishlistButton: {
    position: "absolute",
    top: Spacing.sm,
    right: Spacing.sm,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    ...Shadows.sm,
  },
  heartIcon: {
    fontSize: 16,
    fontWeight: "bold",
  },
  customizableBadge: {
    position: "absolute",
    top: Spacing.sm,
    left: Spacing.sm,
    paddingHorizontal: Spacing.sm,
    paddingVertical: 4,
    borderRadius: BorderRadius.sm,
  },
  customizableBadgeText: {
    fontSize: 10,
    fontWeight: "bold",
  },
  productInfo: {
    padding: Spacing.md,
  },
  productName: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: Spacing.sm,
    minHeight: 32,
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: Spacing.sm,
  },
  price: {
    fontSize: 16,
    fontWeight: "bold",
  },
  addToCartButton: {
    height: 36,
    borderRadius: BorderRadius.md,
    justifyContent: "center",
    alignItems: "center",
  },
  addToCartText: {
    fontSize: 12,
    fontWeight: "600",
  },
});
