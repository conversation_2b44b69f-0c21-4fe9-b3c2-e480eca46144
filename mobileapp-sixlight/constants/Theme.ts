/**
 * Six Light Media Store - Design System & Theme
 * Extraordinary UI theme configuration with modern design principles
 */

export const Colors = {
  // Primary Brand Colors - Red Theme
  primary: {
    50: "#FEF2F2",
    100: "#FEE2E2",
    200: "#FECACA",
    300: "#FCA5A5",
    400: "#F87171",
    500: "#EF4444", // Main brand color - Red
    600: "#DC2626",
    700: "#B91C1C",
    800: "#991B1B",
    900: "#7F1D1D",
    950: "#450A0A",
  },

  // Secondary Colors - Black/Gray Scale
  secondary: {
    50: "#F9FAFB",
    100: "#F3F4F6",
    200: "#E5E7EB",
    300: "#D1D5DB",
    400: "#9CA3AF",
    500: "#6B7280",
    600: "#4B5563",
    700: "#374151",
    800: "#1F2937",
    900: "#111827",
  },

  // Accent Colors
  accent: {
    purple: "#8B5CF6",
    pink: "#EC4899",
    orange: "#F97316",
    green: "#10B981",
    yellow: "#F59E0B",
    red: "#EF4444",
  },

  // Semantic Colors
  success: "#10B981",
  warning: "#F59E0B",
  error: "#EF4444",
  info: "#EF4444", // Changed to red

  // Neutral Colors
  white: "#FFFFFF",
  black: "#000000",
  transparent: "transparent",

  // Background Colors
  background: {
    primary: "#FFFFFF", // White
    secondary: "#F9FAFB", // Very light gray
    tertiary: "#F3F4F6", // Light gray
  },

  // Text Colors
  text: {
    primary: "#000000", // Black
    secondary: "#374151", // Dark gray
    tertiary: "#6B7280", // Medium gray
    inverse: "#FFFFFF", // White
  },

  // Border Colors
  border: {
    light: "#E5E7EB",
    medium: "#D1D5DB",
    dark: "#9CA3AF",
  },

  // Dark Theme Colors
  dark: {
    background: {
      primary: "#000000", // Pure black
      secondary: "#111827", // Very dark gray
      tertiary: "#1F2937", // Dark gray
    },
    text: {
      primary: "#FFFFFF", // White
      secondary: "#F3F4F6", // Light gray
      tertiary: "#D1D5DB", // Medium gray
    },
    border: {
      light: "#374151",
      medium: "#4B5563",
      dark: "#6B7280",
    },
  },
};

export const Typography = {
  // Font Families
  fontFamily: {
    regular: "System",
    medium: "System",
    semiBold: "System",
    bold: "System",
    mono: "SpaceMono",
  },

  // Font Sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    "2xl": 24,
    "3xl": 30,
    "4xl": 36,
    "5xl": 48,
    "6xl": 60,
  },

  // Line Heights
  lineHeight: {
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2,
  },

  // Font Weights
  fontWeight: {
    normal: "400",
    medium: "500",
    semibold: "600",
    bold: "700",
    extrabold: "800",
  },
};

export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  "2xl": 48,
  "3xl": 64,
  "4xl": 80,
  "5xl": 96,
};

export const BorderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  "2xl": 24,
  "3xl": 32,
  full: 9999,
};

export const Shadows = {
  sm: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 8,
  },
  xl: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.25,
    shadowRadius: 25,
    elevation: 12,
  },
};

export const Layout = {
  // Screen Dimensions
  window: {
    width: 0, // Will be set dynamically
    height: 0, // Will be set dynamically
  },

  // Container Sizes
  container: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
  },

  // Header Heights
  header: {
    default: 56,
    large: 72,
  },

  // Tab Bar Height
  tabBar: {
    height: 80,
  },

  // Safe Area
  safeArea: {
    top: 0, // Will be set dynamically
    bottom: 0, // Will be set dynamically
  },
};

export const Animation = {
  // Duration
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
  },

  // Easing
  easing: {
    linear: "linear",
    ease: "ease",
    easeIn: "ease-in",
    easeOut: "ease-out",
    easeInOut: "ease-in-out",
  },
};

// Component Variants
export const ComponentVariants = {
  button: {
    primary: {
      backgroundColor: Colors.primary[500], // Red
      color: Colors.white,
    },
    secondary: {
      backgroundColor: Colors.white,
      color: Colors.black,
    },
    outline: {
      backgroundColor: Colors.transparent,
      borderColor: Colors.primary[500], // Red border
      color: Colors.primary[500], // Red text
    },
    ghost: {
      backgroundColor: Colors.transparent,
      color: Colors.primary[500], // Red text
    },
  },

  card: {
    default: {
      backgroundColor: Colors.white,
      borderRadius: BorderRadius.lg,
      ...Shadows.md,
    },
    elevated: {
      backgroundColor: Colors.white,
      borderRadius: BorderRadius.xl,
      ...Shadows.lg,
    },
  },
};

// Theme Configuration
export const Theme = {
  colors: Colors,
  typography: Typography,
  spacing: Spacing,
  borderRadius: BorderRadius,
  shadows: Shadows,
  layout: Layout,
  animation: Animation,
  components: ComponentVariants,
};

export default Theme;
