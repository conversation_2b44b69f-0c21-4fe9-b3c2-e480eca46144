/**
 * Six Light Media Store - App Context
 * Global state management for the application
 */

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, Product, Category, Order, setAuthToken } from '@/services/api';

// Types
interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  customization?: any;
  price: number;
}

interface AppState {
  // Authentication
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Products & Categories
  products: Product[];
  categories: Category[];
  featuredProducts: Product[];
  
  // Cart
  cart: CartItem[];
  cartTotal: number;
  cartCount: number;
  
  // Orders
  orders: Order[];
  
  // UI State
  isOnline: boolean;
  lastSync: Date | null;
  
  // Search & Filters
  searchQuery: string;
  selectedCategory: string | null;
  
  // Wishlist
  wishlist: Product[];
}

type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_PRODUCTS'; payload: Product[] }
  | { type: 'SET_CATEGORIES'; payload: Category[] }
  | { type: 'SET_FEATURED_PRODUCTS'; payload: Product[] }
  | { type: 'ADD_TO_CART'; payload: CartItem }
  | { type: 'REMOVE_FROM_CART'; payload: string }
  | { type: 'UPDATE_CART_ITEM'; payload: { id: string; quantity: number } }
  | { type: 'CLEAR_CART' }
  | { type: 'SET_ORDERS'; payload: Order[] }
  | { type: 'SET_ONLINE_STATUS'; payload: boolean }
  | { type: 'SET_LAST_SYNC'; payload: Date }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_SELECTED_CATEGORY'; payload: string | null }
  | { type: 'ADD_TO_WISHLIST'; payload: Product }
  | { type: 'REMOVE_FROM_WISHLIST'; payload: number }
  | { type: 'RESTORE_STATE'; payload: Partial<AppState> };

const initialState: AppState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  products: [],
  categories: [],
  featuredProducts: [],
  cart: [],
  cartTotal: 0,
  cartCount: 0,
  orders: [],
  isOnline: true,
  lastSync: null,
  searchQuery: '',
  selectedCategory: null,
  wishlist: [],
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
      
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
      };
      
    case 'SET_PRODUCTS':
      return { ...state, products: action.payload };
      
    case 'SET_CATEGORIES':
      return { ...state, categories: action.payload };
      
    case 'SET_FEATURED_PRODUCTS':
      return { ...state, featuredProducts: action.payload };
      
    case 'ADD_TO_CART': {
      const existingItem = state.cart.find(item => 
        item.product.id === action.payload.product.id &&
        JSON.stringify(item.customization) === JSON.stringify(action.payload.customization)
      );
      
      let newCart: CartItem[];
      if (existingItem) {
        newCart = state.cart.map(item =>
          item.id === existingItem.id
            ? { ...item, quantity: item.quantity + action.payload.quantity }
            : item
        );
      } else {
        newCart = [...state.cart, action.payload];
      }
      
      const cartTotal = newCart.reduce((total, item) => total + (item.price * item.quantity), 0);
      const cartCount = newCart.reduce((count, item) => count + item.quantity, 0);
      
      return {
        ...state,
        cart: newCart,
        cartTotal,
        cartCount,
      };
    }
    
    case 'REMOVE_FROM_CART': {
      const newCart = state.cart.filter(item => item.id !== action.payload);
      const cartTotal = newCart.reduce((total, item) => total + (item.price * item.quantity), 0);
      const cartCount = newCart.reduce((count, item) => count + item.quantity, 0);
      
      return {
        ...state,
        cart: newCart,
        cartTotal,
        cartCount,
      };
    }
    
    case 'UPDATE_CART_ITEM': {
      const newCart = state.cart.map(item =>
        item.id === action.payload.id
          ? { ...item, quantity: action.payload.quantity }
          : item
      ).filter(item => item.quantity > 0);
      
      const cartTotal = newCart.reduce((total, item) => total + (item.price * item.quantity), 0);
      const cartCount = newCart.reduce((count, item) => count + item.quantity, 0);
      
      return {
        ...state,
        cart: newCart,
        cartTotal,
        cartCount,
      };
    }
    
    case 'CLEAR_CART':
      return {
        ...state,
        cart: [],
        cartTotal: 0,
        cartCount: 0,
      };
      
    case 'SET_ORDERS':
      return { ...state, orders: action.payload };
      
    case 'SET_ONLINE_STATUS':
      return { ...state, isOnline: action.payload };
      
    case 'SET_LAST_SYNC':
      return { ...state, lastSync: action.payload };
      
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };
      
    case 'SET_SELECTED_CATEGORY':
      return { ...state, selectedCategory: action.payload };
      
    case 'ADD_TO_WISHLIST': {
      const isAlreadyInWishlist = state.wishlist.some(item => item.id === action.payload.id);
      if (isAlreadyInWishlist) return state;
      
      return {
        ...state,
        wishlist: [...state.wishlist, action.payload],
      };
    }
    
    case 'REMOVE_FROM_WISHLIST':
      return {
        ...state,
        wishlist: state.wishlist.filter(item => item.id !== action.payload),
      };
      
    case 'RESTORE_STATE':
      return { ...state, ...action.payload };
      
    default:
      return state;
  }
}

// Context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  
  // Helper functions
  login: (user: User, token: string) => Promise<void>;
  logout: () => Promise<void>;
  addToCart: (product: Product, quantity?: number, customization?: any) => void;
  removeFromCart: (itemId: string) => void;
  updateCartItem: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  addToWishlist: (product: Product) => void;
  removeFromWishlist: (productId: number) => void;
  isInWishlist: (productId: number) => boolean;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Storage keys
const STORAGE_KEYS = {
  USER: '@sixlight_user',
  TOKEN: '@sixlight_token',
  CART: '@sixlight_cart',
  WISHLIST: '@sixlight_wishlist',
};

// Provider
export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Initialize app state from storage
  useEffect(() => {
    initializeApp();
  }, []);

  // Persist cart to storage when it changes
  useEffect(() => {
    if (!state.isLoading) {
      AsyncStorage.setItem(STORAGE_KEYS.CART, JSON.stringify(state.cart));
    }
  }, [state.cart, state.isLoading]);

  // Persist wishlist to storage when it changes
  useEffect(() => {
    if (!state.isLoading) {
      AsyncStorage.setItem(STORAGE_KEYS.WISHLIST, JSON.stringify(state.wishlist));
    }
  }, [state.wishlist, state.isLoading]);

  const initializeApp = async () => {
    try {
      // Load user and token
      const [userJson, token, cartJson, wishlistJson] = await Promise.all([
        AsyncStorage.getItem(STORAGE_KEYS.USER),
        AsyncStorage.getItem(STORAGE_KEYS.TOKEN),
        AsyncStorage.getItem(STORAGE_KEYS.CART),
        AsyncStorage.getItem(STORAGE_KEYS.WISHLIST),
      ]);

      const restoredState: Partial<AppState> = {};

      if (userJson && token) {
        const user = JSON.parse(userJson);
        setAuthToken(token);
        restoredState.user = user;
        restoredState.isAuthenticated = true;
      }

      if (cartJson) {
        const cart = JSON.parse(cartJson);
        const cartTotal = cart.reduce((total: number, item: CartItem) => 
          total + (item.price * item.quantity), 0);
        const cartCount = cart.reduce((count: number, item: CartItem) => 
          count + item.quantity, 0);
        
        restoredState.cart = cart;
        restoredState.cartTotal = cartTotal;
        restoredState.cartCount = cartCount;
      }

      if (wishlistJson) {
        restoredState.wishlist = JSON.parse(wishlistJson);
      }

      dispatch({ type: 'RESTORE_STATE', payload: restoredState });
    } catch (error) {
      console.error('Failed to initialize app state:', error);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const login = async (user: User, token: string) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
      await AsyncStorage.setItem(STORAGE_KEYS.TOKEN, token);
      setAuthToken(token);
      dispatch({ type: 'SET_USER', payload: user });
    } catch (error) {
      console.error('Failed to save login data:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await AsyncStorage.multiRemove([STORAGE_KEYS.USER, STORAGE_KEYS.TOKEN]);
      setAuthToken(null);
      dispatch({ type: 'SET_USER', payload: null });
      dispatch({ type: 'CLEAR_CART' });
    } catch (error) {
      console.error('Failed to logout:', error);
    }
  };

  const addToCart = (product: Product, quantity = 1, customization?: any) => {
    const cartItem: CartItem = {
      id: `${product.id}-${Date.now()}-${Math.random()}`,
      product,
      quantity,
      customization,
      price: product.price,
    };
    dispatch({ type: 'ADD_TO_CART', payload: cartItem });
  };

  const removeFromCart = (itemId: string) => {
    dispatch({ type: 'REMOVE_FROM_CART', payload: itemId });
  };

  const updateCartItem = (itemId: string, quantity: number) => {
    dispatch({ type: 'UPDATE_CART_ITEM', payload: { id: itemId, quantity } });
  };

  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  const addToWishlist = (product: Product) => {
    dispatch({ type: 'ADD_TO_WISHLIST', payload: product });
  };

  const removeFromWishlist = (productId: number) => {
    dispatch({ type: 'REMOVE_FROM_WISHLIST', payload: productId });
  };

  const isInWishlist = (productId: number) => {
    return state.wishlist.some(item => item.id === productId);
  };

  const value: AppContextType = {
    state,
    dispatch,
    login,
    logout,
    addToCart,
    removeFromCart,
    updateCartItem,
    clearCart,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

export default AppContext;
