/**
 * Six Light Media Store - Theme Context
 * Provides theme management and dark/light mode switching
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Theme, Colors } from '@/constants/Theme';

type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: typeof Theme;
  isDark: boolean;
  themeMode: ThemeMode;
  setThemeMode: (mode: ThemeMode) => void;
  colors: typeof Colors;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = '@sixlight_theme_mode';

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeModeState] = useState<ThemeMode>('system');
  const [isLoading, setIsLoading] = useState(true);

  // Determine if dark mode should be active
  const isDark = themeMode === 'dark' || (themeMode === 'system' && systemColorScheme === 'dark');

  // Create theme object with current mode
  const currentTheme = {
    ...Theme,
    colors: isDark ? {
      ...Colors,
      background: Colors.dark.background,
      text: Colors.dark.text,
      border: Colors.dark.border,
    } : Colors,
  };

  // Load saved theme mode from storage
  useEffect(() => {
    loadThemeMode();
  }, []);

  // Save theme mode to storage when it changes
  useEffect(() => {
    if (!isLoading) {
      saveThemeMode(themeMode);
    }
  }, [themeMode, isLoading]);

  const loadThemeMode = async () => {
    try {
      const savedMode = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (savedMode && ['light', 'dark', 'system'].includes(savedMode)) {
        setThemeModeState(savedMode as ThemeMode);
      }
    } catch (error) {
      console.warn('Failed to load theme mode:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveThemeMode = async (mode: ThemeMode) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
    } catch (error) {
      console.warn('Failed to save theme mode:', error);
    }
  };

  const setThemeMode = (mode: ThemeMode) => {
    setThemeModeState(mode);
  };

  const value: ThemeContextType = {
    theme: currentTheme,
    isDark,
    themeMode,
    setThemeMode,
    colors: currentTheme.colors,
  };

  if (isLoading) {
    return null; // Or a loading component
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Hook for getting theme-aware styles
export function useThemedStyles<T>(
  stylesFn: (theme: typeof Theme, isDark: boolean) => T
): T {
  const { theme, isDark } = useTheme();
  return stylesFn(theme, isDark);
}

// Utility function to create theme-aware styles
export function createThemedStyles<T>(
  stylesFn: (theme: typeof Theme, isDark: boolean) => T
) {
  return stylesFn;
}

export default ThemeContext;
