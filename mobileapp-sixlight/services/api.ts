/**
 * Six Light Media Store - API Service
 * Centralized API client for backend communication
 */

// Note: Constants import removed as we're using direct environment variables

// Environment-aware API Configuration
const getApiUrl = () => {
  // Check if we're in development mode
  const isDevelopment = __DEV__ || process.env.NODE_ENV === "development";

  // Use explicit URL if set
  if (process.env.EXPO_PUBLIC_API_URL) {
    return process.env.EXPO_PUBLIC_API_URL;
  }

  // Use environment-specific URLs
  if (isDevelopment) {
    return process.env.EXPO_PUBLIC_API_URL_DEV || "http://localhost:3001";
  } else {
    return (
      process.env.EXPO_PUBLIC_API_URL_PROD ||
      "https://backendapi-sixlight.onrender.com"
    );
  }
};

const API_BASE_URL = getApiUrl();

// Debug log for API URL (only in development)
if (__DEV__) {
  console.log("🌐 Environment Detection:", {
    isDevelopment: __DEV__,
    NODE_ENV: process.env.NODE_ENV,
    selectedUrl: API_BASE_URL,
    availableUrls: {
      dev: process.env.EXPO_PUBLIC_API_URL_DEV,
      prod: process.env.EXPO_PUBLIC_API_URL_PROD,
      explicit: process.env.EXPO_PUBLIC_API_URL,
    },
  });
}

// Types
export interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  customizable: boolean;
  slug: string;
  modelUrl?: string;
}

export interface Category {
  id: number;
  name: string;
}

export interface User {
  id: number;
  email: string;
  name: string;
  role: "USER" | "ADMIN";
  profileImage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Order {
  id: number;
  userId: number;
  productId: number;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  customerAddress?: string;
  customColor?: string;
  customText?: string;
  isCustomized?: boolean;
  customizationData?: string; // JSON string containing Fabric.js canvas data
  customizationPreview?: string; // Base64 image preview
  quantity: number;
  unitPrice?: number;
  totalPrice?: number;
  status: "PENDING" | "COLLECTED";
  createdAt: string;
  updatedAt: string;
  product: Product;
  user?: User;
}

export interface AuthResponse {
  access_token: string;
  user: User;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
}

// API Client Class
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  setToken(token: string | null) {
    this.token = token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const maxRetries = 3;
    const baseTimeout = 30000; // 30 seconds for initial request
    const retryTimeout = 15000; // 15 seconds for retries

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...(options.headers as Record<string, string>),
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    try {
      // Add timeout handling with longer timeout for first request
      const controller = new AbortController();
      const timeout = retryCount === 0 ? baseTimeout : retryTimeout;
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      console.log(
        `🌐 API Request (attempt ${retryCount + 1}/${maxRetries + 1}):`,
        {
          url,
          timeout: `${timeout / 1000}s`,
          retryCount,
        }
      );

      const response = await fetch(url, {
        ...config,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      const data = await response.json();
      console.log(`✅ API Request successful:`, {
        url,
        dataLength: JSON.stringify(data).length,
      });
      return data;
    } catch (error) {
      // Enhanced error logging with more context
      console.error("❌ API Request failed:", {
        url,
        endpoint,
        error: error instanceof Error ? error.message : error,
        baseURL: this.baseURL,
        attempt: retryCount + 1,
        maxRetries: maxRetries + 1,
      });

      // Retry logic for timeout errors
      if (
        error instanceof Error &&
        error.name === "AbortError" &&
        retryCount < maxRetries
      ) {
        console.log(
          `🔄 Retrying request in ${(retryCount + 1) * 2} seconds...`
        );
        await new Promise((resolve) =>
          setTimeout(resolve, (retryCount + 1) * 2000)
        ); // Exponential backoff
        return this.request<T>(endpoint, options, retryCount + 1);
      }

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.name === "AbortError") {
          throw new Error(
            "Server is taking too long to respond. This might be due to the server starting up. Please try again in a moment."
          );
        }
        if (error.message.includes("Network request failed")) {
          throw new Error(
            "Network error - please check your internet connection"
          );
        }
      }

      throw error;
    }
  }

  // Authentication
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>("/auth/login", {
      method: "POST",
      body: JSON.stringify(credentials),
    });
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    return this.request<AuthResponse>("/auth/register", {
      method: "POST",
      body: JSON.stringify(userData),
    });
  }

  async getProfile(): Promise<User> {
    return this.request<User>("/auth/me", {
      method: "POST",
    });
  }

  // Products
  async getProducts(params?: {
    category?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<Product[]> {
    return this.request<Product[]>("/product");
  }

  async getProduct(id: number): Promise<Product> {
    return this.request<Product>(`/product/${id}`);
  }

  async getProductBySlug(slug: string): Promise<Product> {
    return this.request<Product>(`/product/${slug}`);
  }

  // Categories
  async getCategories(): Promise<Category[]> {
    return this.request<Category[]>("/categories");
  }

  async getCategory(id: number): Promise<Category> {
    return this.request<Category>(`/categories/${id}`);
  }

  // Orders
  async createOrder(orderData: {
    productId: number;
    customerName: string;
    customerPhone?: string;
    customerAddress?: string;
    customColor?: string;
    customText?: string;
    isCustomized?: boolean;
    customizationData?: string;
    customizationPreview?: string;
    quantity?: number;
  }): Promise<Order> {
    return this.request<Order>("/user/orders", {
      method: "POST",
      body: JSON.stringify(orderData),
    });
  }

  async getOrders(): Promise<Order[]> {
    return this.request<Order[]>("/user/orders");
  }

  async getUserDashboard(): Promise<{ user: User; orders: Order[] }> {
    return this.request<{ user: User; orders: Order[] }>("/user/dashboard");
  }

  async deleteAccount(): Promise<{ success: boolean }> {
    return this.request<{ success: boolean }>("/user/delete", {
      method: "DELETE",
    });
  }

  // Admin endpoints
  async createProduct(productData: {
    name: string;
    description: string;
    price: number;
    category: string;
    customizable: boolean;
    slug: string;
    image?: string;
    modelUrl?: string;
  }): Promise<Product> {
    return this.request<Product>("/product", {
      method: "POST",
      body: JSON.stringify(productData),
    });
  }

  async updateProduct(
    id: number,
    productData: Partial<{
      name: string;
      description: string;
      price: number;
      category: string;
      customizable: boolean;
      slug: string;
      image?: string;
      modelUrl?: string;
    }>
  ): Promise<Product> {
    return this.request<Product>(`/product/${id}`, {
      method: "PUT",
      body: JSON.stringify(productData),
    });
  }

  async deleteProduct(id: number): Promise<void> {
    return this.request<void>(`/product/${id}`, {
      method: "DELETE",
    });
  }

  async getAllOrders(): Promise<Order[]> {
    return this.request<Order[]>("/admin/orders");
  }

  async updateOrderStatus(
    id: number,
    status: "PENDING" | "COLLECTED"
  ): Promise<Order> {
    return this.request<Order>(`/admin/orders/${id}/status`, {
      method: "PUT",
      body: JSON.stringify({ status }),
    });
  }

  async deleteOrder(id: number): Promise<void> {
    return this.request<void>(`/admin/orders/${id}`, {
      method: "DELETE",
    });
  }

  async getAdminDashboard(): Promise<{
    stats: {
      users: number;
      orders: number;
      products: number;
      collectedOrders: number;
    };
    recentOrders: Order[];
    recentUsers: User[];
  }> {
    return this.request<any>("/admin/dashboard");
  }

  // Health Check - using categories endpoint as health check since /health doesn't exist
  async healthCheck(): Promise<any> {
    return this.request<any>("/categories");
  }

  // Warm up server (useful for cold starts)
  async warmUp(): Promise<void> {
    try {
      console.log("🔥 Warming up server...");
      await this.healthCheck();
      console.log("✅ Server is warmed up");
    } catch (error) {
      console.log("⚠️ Server warm-up failed, but continuing...");
    }
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Utility functions
export const setAuthToken = (token: string | null) => {
  apiClient.setToken(token);
};

export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(price);
};

export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

export const getImageUrl = (imagePath: string): string => {
  if (imagePath.startsWith("http")) {
    return imagePath;
  }
  return `${API_BASE_URL}/${imagePath.replace(/^\/+/, "")}`;
};

export default apiClient;
