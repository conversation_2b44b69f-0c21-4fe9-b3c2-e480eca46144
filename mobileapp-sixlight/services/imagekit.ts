/**
 * Six Light Media Store - ImageKit Service
 * Image upload and management service using ImageKit
 */

import ImageKit from "imagekit-javascript";
import Constants from "expo-constants";

// ImageKit configuration (from environment variables)
const IMAGEKIT_CONFIG = {
  publicKey:
    process.env.EXPO_PUBLIC_IMAGEKIT_PUBLIC_KEY ||
    Constants.expoConfig?.extra?.imagekitPublicKey ||
    "public_kFR0vTVL7DIDI8YW9eF6/luGjB4=",
  urlEndpoint:
    process.env.EXPO_PUBLIC_IMAGEKIT_URL_ENDPOINT ||
    Constants.expoConfig?.extra?.imagekitUrlEndpoint ||
    "https://ik.imagekit.io/fwbvmq9re",
  privateKey:
    process.env.EXPO_PUBLIC_IMAGEKIT_PRIVATE_KEY ||
    Constants.expoConfig?.extra?.imagekitPrivateKey ||
    "private_jtsBIKXyuTkkHzHJOBSHLqbyK74=", // Note: In production, this should be handled server-side
};

// Debug log for ImageKit configuration (only in development)
if (__DEV__) {
  console.log("🖼️ ImageKit Configuration:", {
    publicKey: IMAGEKIT_CONFIG.publicKey?.substring(0, 20) + "...",
    urlEndpoint: IMAGEKIT_CONFIG.urlEndpoint,
    privateKey: IMAGEKIT_CONFIG.privateKey?.substring(0, 20) + "...",
  });
}

// Initialize ImageKit
const imagekit = new ImageKit({
  publicKey: IMAGEKIT_CONFIG.publicKey,
  urlEndpoint: IMAGEKIT_CONFIG.urlEndpoint,
});

export interface ImageUploadResult {
  url: string;
  fileId: string;
  name: string;
  size: number;
  filePath: string;
  tags?: string[];
  isPrivateFile?: boolean;
  customCoordinates?: string;
  responseFields?: any;
}

export interface ImageUploadOptions {
  fileName?: string;
  folder?: string;
  tags?: string[];
  isPrivateFile?: boolean;
  customCoordinates?: string;
  responseFields?: string[];
  extensions?: any[];
  webhookUrl?: string;
  overwriteFile?: boolean;
  overwriteAITags?: boolean;
  overwriteTags?: boolean;
  overwriteCustomMetadata?: boolean;
  customMetadata?: Record<string, any>;
}

class ImageKitService {
  private imagekit: ImageKit;

  constructor() {
    this.imagekit = imagekit;
  }

  /**
   * Get authentication parameters for ImageKit upload
   * In production, this should be fetched from your backend
   */
  async getAuthenticationParameters(): Promise<{
    signature: string;
    expire: number;
    token: string;
  }> {
    // For now, we'll use a simple implementation
    // In production, you should call your backend API to get these parameters
    const expire = Math.floor(Date.now() / 1000) + 2400; // 40 minutes from now

    // Note: In production, the signature should be generated server-side
    // This is a simplified implementation for development
    return {
      signature: "dummy-signature", // This should come from your backend
      expire,
      token: "dummy-token", // This should come from your backend
    };
  }

  /**
   * Upload image from base64 data
   */
  async uploadImage(
    base64Data: string,
    options: ImageUploadOptions = {}
  ): Promise<ImageUploadResult> {
    try {
      // Get authentication parameters
      const authParams = await this.getAuthenticationParameters();

      // Prepare upload options
      const uploadOptions = {
        file: base64Data,
        fileName: options.fileName || `image_${Date.now()}.jpg`,
        folder: options.folder || "/products",
        tags: options.tags || ["mobile-upload"],
        isPrivateFile: options.isPrivateFile || false,
        overwriteFile: options.overwriteFile || false,
        ...authParams,
      };

      // Upload the image
      const result = await this.imagekit.upload(uploadOptions);

      return {
        url: result.url,
        fileId: result.fileId,
        name: result.name,
        size: result.size,
        filePath: result.filePath,
        tags: result.tags,
        isPrivateFile: result.isPrivateFile,
        customCoordinates: result.customCoordinates,
        responseFields: result,
      };
    } catch (error) {
      console.error("ImageKit upload error:", error);
      throw new Error(
        `Failed to upload image: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Upload image from file URI (mobile specific)
   */
  async uploadImageFromUri(
    fileUri: string,
    options: ImageUploadOptions = {}
  ): Promise<ImageUploadResult> {
    try {
      // Convert file URI to base64
      const base64Data = await this.convertUriToBase64(fileUri);

      // Upload using base64 method
      return await this.uploadImage(base64Data, options);
    } catch (error) {
      console.error("ImageKit upload from URI error:", error);
      throw new Error(
        `Failed to upload image from URI: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Convert file URI to base64 string
   */
  private async convertUriToBase64(uri: string): Promise<string> {
    try {
      const response = await fetch(uri);
      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64 = reader.result as string;
          // Remove data URL prefix if present
          const base64Data = base64.includes(",")
            ? base64.split(",")[1]
            : base64;
          resolve(base64Data);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      throw new Error(
        `Failed to convert URI to base64: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Get optimized image URL with transformations
   */
  getOptimizedImageUrl(
    imagePath: string,
    transformations: {
      width?: number;
      height?: number;
      quality?: number;
      format?: "jpg" | "png" | "webp" | "avif";
      crop?: "maintain_ratio" | "force" | "at_least" | "at_max";
      cropMode?: "resize" | "extract" | "pad_extract" | "pad_resize";
      focus?:
        | "center"
        | "top"
        | "left"
        | "bottom"
        | "right"
        | "top_left"
        | "top_right"
        | "bottom_left"
        | "bottom_right";
      background?: string;
      border?: string;
      rotation?: number;
      blur?: number;
      named?: string;
    } = {}
  ): string {
    try {
      const transformationString = Object.entries(transformations)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => {
          // Map transformation keys to ImageKit format
          const keyMap: Record<string, string> = {
            width: "w",
            height: "h",
            quality: "q",
            format: "f",
            crop: "c",
            cropMode: "cm",
            focus: "fo",
            background: "bg",
            border: "b",
            rotation: "rt",
            blur: "bl",
            named: "n",
          };

          const transformKey = keyMap[key] || key;
          return `${transformKey}-${value}`;
        })
        .join(",");

      const baseUrl = IMAGEKIT_CONFIG.urlEndpoint;
      const transformationPrefix = transformationString
        ? `tr:${transformationString}/`
        : "";

      // Remove leading slash from imagePath if present
      const cleanImagePath = imagePath.startsWith("/")
        ? imagePath.slice(1)
        : imagePath;

      return `${baseUrl}/${transformationPrefix}${cleanImagePath}`;
    } catch (error) {
      console.error("Error generating optimized image URL:", error);
      return imagePath; // Return original path as fallback
    }
  }

  /**
   * Delete image by file ID
   */
  async deleteImage(fileId: string): Promise<boolean> {
    try {
      // Note: Delete operations require server-side implementation
      // This is a placeholder for the mobile app
      console.warn("Image deletion should be implemented server-side");
      return true;
    } catch (error) {
      console.error("ImageKit delete error:", error);
      return false;
    }
  }

  /**
   * List images in a folder
   */
  async listImages(
    folder: string = "/products",
    limit: number = 20
  ): Promise<any[]> {
    try {
      // Note: Listing operations require server-side implementation
      // This is a placeholder for the mobile app
      console.warn("Image listing should be implemented server-side");
      return [];
    } catch (error) {
      console.error("ImageKit list error:", error);
      return [];
    }
  }
}

// Export singleton instance
export const imagekitService = new ImageKitService();

// Export utility functions
export const getOptimizedImageUrl = (
  imagePath: string,
  transformations = {}
) => {
  return imagekitService.getOptimizedImageUrl(imagePath, transformations);
};

export const uploadImage = (
  base64Data: string,
  options: ImageUploadOptions = {}
) => {
  return imagekitService.uploadImage(base64Data, options);
};

export const uploadImageFromUri = (
  fileUri: string,
  options: ImageUploadOptions = {}
) => {
  return imagekitService.uploadImageFromUri(fileUri, options);
};

export default imagekitService;
